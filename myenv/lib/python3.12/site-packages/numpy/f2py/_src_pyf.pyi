import re
from collections.abc import Mapping
from typing import Final

from _typeshed import Str<PERSON>r<PERSON>ytesPath

routine_start_re: Final[re.Pattern[str]] = ...
routine_end_re: Final[re.Pattern[str]] = ...
function_start_re: Final[re.Pattern[str]] = ...
template_re: Final[re.Pattern[str]] = ...
named_re: Final[re.Pattern[str]] = ...
list_re: Final[re.Pattern[str]] = ...
item_re: Final[re.Pattern[str]] = ...
template_name_re: Final[re.Pattern[str]] = ...
include_src_re: Final[re.Pattern[str]] = ...

def parse_structure(astr: str) -> list[tuple[int, int]]: ...
def find_repl_patterns(astr: str) -> dict[str, str]: ...
def find_and_remove_repl_patterns(astr: str) -> tuple[str, dict[str, str]]: ...
def conv(astr: str) -> str: ...

#
def unique_key(adict: Mapping[str, object]) -> str: ...
def expand_sub(substr: str, names: dict[str, str]) -> str: ...
def process_str(allstr: str) -> str: ...

#
def resolve_includes(source: StrOrBytesPath) -> list[str]: ...
def process_file(source: StrOrBytesPath) -> str: ...
