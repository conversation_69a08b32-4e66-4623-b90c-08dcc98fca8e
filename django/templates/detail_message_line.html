{% extends "layout/base.html" %}
{% load static %}

{% block content %}
<div class="container py-4">
  <div class="row g-4">
    <!-- Form -->
    <div class="col-lg-8">
      <div class="card shadow-sm p-4">
        <form method="post" action="{% url 'store_message_line' %}">
          {% csrf_token %}
          <div class="mb-3">
            <label class="form-label">取引先コード (複数ある場合はカンマ区切り)</label>
            <input type="text"  name="customer_codes" class="form-control" value="{{ customer_codes_str }}" autocomplete="off">
          </div>

          <div class="mb-3">
            <label class="form-label">配信日付</label>
            <input name="delivery_datetime" id="delivery_datetime" type="text" class="form-control" value="{{ line_message.delivery_datetime }}" autocomplete="off">
          </div>
          <div class="mb-3">
            <label class="form-label">管理用タイトル</label>
            <input name="management_title" id="management_title" type="text" class="form-control" value="{{ line_message.management_title }}" autocomplete="off">
          </div>

          <div class="btn-group w-20 mb-3" role="group" aria-label="選択">
            <input type="radio" class="btn-check" name="format_type" id="option1" autocomplete="off" {% if line_message.format_type == '商品選択' %}checked{% endif %} value="商品選択">
            <label class="btn btn-outline-secondary select_product"  for="option1">商品選択</label>

            <input type="radio" class="btn-check" name="format_type" id="option2" autocomplete="off" {% if line_message.format_type == 'カスタム' %}checked{% endif %}  value="カスタム">
            <label class="btn btn-outline-secondary select_product"  for="option2">カスタム</label>
          </div>

          <div class="row mb-3">
            <div class="col-9">
              <input
                      id="product-code-input"
                      name="product_code"
                      autocomplete="off"
                      class="form-control"
                      list="datalistOptions"
                      placeholder="商品コードを入力する"
                      value="{{ line_message.product_code }}"
              />
              <datalist id="datalistOptions">
                {% for product in products %}
                <option value="{{product.product_code}}"></option>
                {% endfor %}
              </datalist>
            </div>
            <div class="col-3">
              <button id="get-product-info" class="btn btn-outline-danger w-100" type="button">情報取得</button>
            </div>
          </div>
          <div class="mb-3">
            <label class="form-label d-block">画像(画像変更する場合は画像をクリックしてください)</label>
            <img data-images="{{ image }}" width="400" height="200" id="product-image"  src="{% static 'image/preview_default.png' %}" alt="preview" class="preview-image img-fluid rounded">
          </div>

          <div class="mb-3">
            <label class="form-label">タイトル</label>
            <input name="product_title" id="product-title" type="text" class="form-control" value="{{ line_message.product_title }}" autocomplete="off">
          </div>

          <div class="mb-3">
            <label class="form-label">申出し</label>
            <input name="product_price" id="product-price" type="text" class="form-control" value="{{ line_message.product_price }}" autocomplete="off">
          </div>

          <div class="mb-3">
            <label class="form-label">テキスト</label>
            <textarea name="product_description" id="product-description" class="form-control" rows="2" autocomplete="off">{{ line_message.product_description }}</textarea>
          </div>

          <div class="mb-3">
            <label class="form-label">遷移先URL</label>
            <input name="product_url" id="product-url" type="text" class="form-control" value="{{ line_message.product_url }}" autocomplete="off">
          </div>
          <div>
            <button id="submit_create" disabled type="submit" class="btn btn-primary  w-100">送信</button>
          </div>
        </form>
      </div>
    </div>

    <!-- Preview -->
    <div class="col-lg-4">
      <h6 class="mb-2">プレビュー</h6>
      <div class="card preview-card shadow-sm">
        <img  class="preview-image" id="preview-image" src="{% static 'image/preview_default.png' %}" alt="商品画像">
        <div class="card-body">
          <h5 id="preview-title" class="card-title">{{line_message.product_title }}</h5>
          <p id="preview-price" class="card-text text-muted">{{line_message.product_price }}</p>
          <p id="preview-description" class="small text-muted">{{line_message.product_description }}</p>
          <a href="#" class="btn btn-primary w-100">商品を見る</a>
        </div>
      </div>
    </div>
  </div>
{% endblock %}
{% block extra_js%}
<script>
  $(document).ready(function () {
    flatpickr("#delivery_datetime", {
      enableTime: true,                // bật chọn giờ
      dateFormat: "Y-m-d H:i:S",       // format theo Django
      time_24hr: true,                 // giờ 24h
      enableSeconds: true,          // bật giây
      allowInput: true,
      locale: "ja",
    });

    $('#get-product-info').click(function () {
      const productCode = $('#product-code-input').val().trim();

      if (!productCode) {
        alert('商品コードを入力してください');
        return;
      }

      // Show loading state
      $(this).prop('disabled', true).text('取得中...');

      $.ajax({
        url: `/api/products/${productCode}/`,
        type: 'GET',
        success: function (data) {
          $('#product-url').val(data.url);
          if (data.image !== '') {
            checkImage(data.image, function (exists) {
              if (exists) {
                $('.preview-image').attr('src', data.image);
              }
            });
          } else {
            $('.preview-image ').attr('src', '/static/image/preview_default.png');
          }
          $('#submit_create').prop('disabled', false);
        },
        error: function (xhr, status, error) {
          if (xhr.status === 404) {
            alert('商品が見つかりませんでした');
          } else {
            alert('エラーが発生しました: ' + error);
          }
          $('#product-url').val('');

          // Update preview
        },
        complete: function () {
          // Reset button state
          $('#get-product-info').prop('disabled', false).text('情報取得');
        }
      });
    });
    function checkImage(url, callback) {
      var img = new Image();
      img.onload = function () {
        callback(true);
      };
      img.onerror = function () {
        callback(false);
      };
      img.src = url;
    }
    $('#product-title').on('input', function () {
      $('#preview-title').text($(this).val());
    });
    $('#product-price').on('input', function () {
      $('#preview-price').text($(this).val());
    });$('#product-description').on('input', function () {
      $('#preview-description').text($(this).val());
    });
    $('#product-code-input').on('change', function () {
          $('.preview-image ').attr('src', '/static/image/preview_default.png');
          $('#product-url').val('');
          $('#submit_create').prop('disabled', true);
      // $('.btn-primary').attr('href', $(this).val());
    });
  });
</script>
{% endblock %}