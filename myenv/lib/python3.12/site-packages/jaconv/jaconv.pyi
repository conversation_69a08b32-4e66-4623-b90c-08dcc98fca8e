import typing
from .compat import map as map
from .conv_table import H2HK_TABLE as H2HK_TABLE, H2K_TABLE as H2K_TABLE, H2Z_A as H2Z_A, H2Z_AD as H2Z_<PERSON>, H2<PERSON>_<PERSON> as H2Z_AK, H2<PERSON>_ALL as H2Z_ALL, H2Z_D as H2Z_D, H2Z_DK as H2Z_DK, H2Z_K as H2Z_K, HEP2KANA as HEP2KANA, JULIUS_LONG_VOWEL as JULIUS_LONG_VOWEL, K2H_TABLE as K2H_TABLE, KANA2HEP as K<PERSON>A<PERSON><PERSON><PERSON>, SMALL_KANA2BIG_KANA as SMALL_KANA2BIG_KANA, Z2H_A as Z2H_A, Z2H_AD as Z2H_AD, Z2H_AK as Z2H_<PERSON>, Z2H_ALL as Z2H_ALL, Z2H_D as Z2H_D, Z2H_DK as Z2<PERSON>_<PERSON>K, Z2<PERSON>_<PERSON> as <PERSON>2<PERSON>_<PERSON>
from _typeshed import Incomplete

consonants: Incomplete
ending_h_pattern: Incomplete

def _exclude_ignorechar(ignore: str, conv_map: typing.Dict[int, str]) -> typing.Dict[int, str]: ...
def _convert(text: str, conv_map: typing.Dict[int, str]) -> str: ...
def _translate(text: str, ignore: str, conv_map: typing.Dict[int, str]) -> str: ...
def hira2kata(text: str, ignore: str = '') -> str: ...
def hira2hkata(text: str, ignore: str = '') -> str: ...
def kata2hira(text: str, ignore: str = '') -> str: ...
def enlargesmallkana(text: str, ignore: str = '') -> str: ...
def h2z(text: str, ignore: str = '', kana: bool = True, ascii: bool = False, digit: bool = False) -> str: ...
def z2h(text: str, ignore: str = '', kana: bool = True, ascii: bool = False, digit: bool = False) -> str: ...
def normalize(text: str, mode: str = 'NFKC') -> str: ...
def kana2alphabet(text: str) -> str: ...
def alphabet2kana(text: str) -> str: ...
def hiragana2julius(text: str) -> str: ...
