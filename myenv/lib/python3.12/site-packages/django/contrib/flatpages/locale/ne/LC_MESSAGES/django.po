# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-10-09 17:42+0200\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Nepali (http://www.transifex.com/django/django/language/ne/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ne\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr ""

msgid "Flat Pages"
msgstr ""

msgid "URL"
msgstr "URL"

msgid ""
"Example: '/about/contact/'. Make sure to have leading and trailing slashes."
msgstr "उदाहरणका लागि: '/about/contact/' । अगाडि र पछाडि '/' राख्न नभुल्नुहोला ।"

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr "यो मानमा अंक, वर्ण, थोप्लो, अन्डरस्कोर, ड्यास, स्ल्यास र टिल्ड मात्र हुनुपर्दछ ।"

msgid "URL is missing a leading slash."
msgstr ""

msgid "URL is missing a trailing slash."
msgstr ""

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr ""

msgid "title"
msgstr "शीर्षक"

msgid "content"
msgstr "विषय"

msgid "enable comments"
msgstr "प्रतिकृयाको लागि मञ्जुरी दिनुहोस"

msgid "template name"
msgstr "टेम्प्लेटको नाम"

msgid ""
"Example: 'flatpages/contact_page.html'. If this isn't provided, the system "
"will use 'flatpages/default.html'."
msgstr ""

msgid "registration required"
msgstr "दर्ता अनिवार्य छ ।"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""

msgid "sites"
msgstr "साइटहरु"

msgid "flat page"
msgstr ""

msgid "flat pages"
msgstr ""
