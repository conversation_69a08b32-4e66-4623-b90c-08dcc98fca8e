# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2023
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2023-12-04 19:22+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2023\n"
"Language-Team: Uyghur (http://app.transifex.com/django/django/language/ug/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ug\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr "مەزمۇن تىپلىرى"

msgid "python model class name"
msgstr "python مودېلىنىڭ سىنىپ ئىسمى"

msgid "content type"
msgstr "مەزمۇن تىپى"

msgid "content types"
msgstr "مەزمۇن تىپلىرى"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "مەزمۇن تىپى %(ct_id)s ئوبيېكتىنىڭ باغلانغان مودېلى يوق"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "مەزمۇن تىپى %(ct_id)s ئوبيېكت %(obj_id)s مەۋجۇت ئەمەس"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "%(ct_name)s ئوبيېكتى get_absolute_url() ئۇسۇلى يوق"
