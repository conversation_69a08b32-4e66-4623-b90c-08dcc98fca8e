# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <j<PERSON><PERSON>@leidel.info>, 2011
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2014
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Bengali (http://www.transifex.com/django/django/language/"
"bn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: bn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Humanize"
msgstr ""

msgid "th"
msgstr "তম"

msgid "st"
msgstr "ম"

msgid "nd"
msgstr "য়"

msgid "rd"
msgstr "য়"

#, python-format
msgid "%(value).1f million"
msgid_plural "%(value).1f million"
msgstr[0] "%(value).1f মিলিয়ন"
msgstr[1] "%(value).1f মিলিয়ন"

#, python-format
msgid "%(value)s million"
msgid_plural "%(value)s million"
msgstr[0] "%(value)s মিলিয়ন"
msgstr[1] "%(value)s মিলিয়ন"

#, python-format
msgid "%(value).1f billion"
msgid_plural "%(value).1f billion"
msgstr[0] "%(value).1f বিলিয়ন"
msgstr[1] "%(value).1f বিলিয়ন"

#, python-format
msgid "%(value)s billion"
msgid_plural "%(value)s billion"
msgstr[0] "%(value)s বিলিয়ন"
msgstr[1] "%(value)s বিলিয়ন"

#, python-format
msgid "%(value).1f trillion"
msgid_plural "%(value).1f trillion"
msgstr[0] "%(value).1f ট্রিলিয়ন"
msgstr[1] "%(value).1f ট্রিলিয়ন"

#, python-format
msgid "%(value)s trillion"
msgid_plural "%(value)s trillion"
msgstr[0] "%(value)s ট্রিলিয়ন"
msgstr[1] "%(value)s ট্রিলিয়ন"

#, python-format
msgid "%(value).1f quadrillion"
msgid_plural "%(value).1f quadrillion"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "%(value)s quadrillion"
msgid_plural "%(value)s quadrillion"
msgstr[0] "%(value)s কোয়াড্রিলিয়ন"
msgstr[1] "%(value)s কোয়াড্রিলিয়ন"

#, python-format
msgid "%(value).1f quintillion"
msgid_plural "%(value).1f quintillion"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "%(value)s quintillion"
msgid_plural "%(value)s quintillion"
msgstr[0] "%(value)s কুইন্টিলিয়ন"
msgstr[1] "%(value)s কুইন্টিলিয়ন"

#, python-format
msgid "%(value).1f sextillion"
msgid_plural "%(value).1f sextillion"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "%(value)s sextillion"
msgid_plural "%(value)s sextillion"
msgstr[0] "%(value)s সেক্সটিলিয়ন"
msgstr[1] "%(value)s সেক্সটিলিয়ন"

#, python-format
msgid "%(value).1f septillion"
msgid_plural "%(value).1f septillion"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "%(value)s septillion"
msgid_plural "%(value)s septillion"
msgstr[0] "%(value)s সেপ্টিলিয়ন"
msgstr[1] "%(value)s সেপ্টিলিয়ন"

#, python-format
msgid "%(value).1f octillion"
msgid_plural "%(value).1f octillion"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "%(value)s octillion"
msgid_plural "%(value)s octillion"
msgstr[0] "%(value)s অক্টিলিয়ন"
msgstr[1] "%(value)s অক্টিলিয়ন"

#, python-format
msgid "%(value).1f nonillion"
msgid_plural "%(value).1f nonillion"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "%(value)s nonillion"
msgid_plural "%(value)s nonillion"
msgstr[0] "%(value)s ননিলিয়ন"
msgstr[1] "%(value)s ননিলিয়ন"

#, python-format
msgid "%(value).1f decillion"
msgid_plural "%(value).1f decillion"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "%(value)s decillion"
msgid_plural "%(value)s decillion"
msgstr[0] "%(value)s ডেসিলিয়ন"
msgstr[1] "%(value)s ডেসিলিয়ন"

#, python-format
msgid "%(value).1f googol"
msgid_plural "%(value).1f googol"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "%(value)s googol"
msgid_plural "%(value)s googol"
msgstr[0] "%(value)s গোগোল"
msgstr[1] "%(value)s গোগোল"

msgid "one"
msgstr "এক"

msgid "two"
msgstr "দুই"

msgid "three"
msgstr "তিন"

msgid "four"
msgstr "চার"

msgid "five"
msgstr "পাঁচ"

msgid "six"
msgstr "ছয়"

msgid "seven"
msgstr "সাত"

msgid "eight"
msgstr "আট"

msgid "nine"
msgstr "নয়"

msgid "today"
msgstr "আজ"

msgid "tomorrow"
msgstr "আগামীকাল"

msgid "yesterday"
msgstr "গতকাল"

#, python-format
msgctxt "naturaltime"
msgid "%(delta)s ago"
msgstr "%(delta)s আগে"

msgid "now"
msgstr "এখন"

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "a second ago"
msgid_plural "%(count)s seconds ago"
msgstr[0] "এক সেকেন্ড আগে"
msgstr[1] "%(count)s সেকেন্ড আগে"

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "a minute ago"
msgid_plural "%(count)s minutes ago"
msgstr[0] "এক মিনিট আগে"
msgstr[1] "%(count)s মিনিট আগে"

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "an hour ago"
msgid_plural "%(count)s hours ago"
msgstr[0] "এক ঘন্টা আগে"
msgstr[1] "%(count)s  ঘন্টা আগে"

#, python-format
msgctxt "naturaltime"
msgid "%(delta)s from now"
msgstr "এখন থেকে %(delta)s পরে"

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "a second from now"
msgid_plural "%(count)s seconds from now"
msgstr[0] "এখন থেকে এক সেকেন্ড পরে"
msgstr[1] "এখন থেকে %(count)s সেকেন্ড পরে"

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "a minute from now"
msgid_plural "%(count)s minutes from now"
msgstr[0] "এখন থেকে এক মিনিট পরে"
msgstr[1] "এখন থেকে %(count)s মিনিট পরে"

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "an hour from now"
msgid_plural "%(count)s hours from now"
msgstr[0] "এখন থেকে এক ঘন্টা পরে"
msgstr[1] "এখন থেকে %(count)s ঘন্টা পরে"
