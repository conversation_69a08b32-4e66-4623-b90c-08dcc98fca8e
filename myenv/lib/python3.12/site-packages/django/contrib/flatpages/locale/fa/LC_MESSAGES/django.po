# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2011-2012
# <PERSON> <<EMAIL>>, 2015
# <PERSON><PERSON> <fariman<PERSON><PERSON><PERSON>@gmail.com>, 2019
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON> <<EMAIL>>, 2014
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2021-04-19 03:16+0000\n"
"Last-Translator: rahim agh <<EMAIL>>\n"
"Language-Team: Persian (http://www.transifex.com/django/django/language/"
"fa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: fa\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

msgid "Advanced options"
msgstr "گزینه‌های پیشرفته"

msgid "Flat Pages"
msgstr "صفحات تخت"

msgid "URL"
msgstr "نشانی اینترنتی"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr ""
"مثال: '/about/contact/'. مطمئن شوید که اسلش را هم در ابتدا و هم در انتها "
"گذاشته‌اید."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr "این مقدار فقط باید حاوی حروف، اعداد، خط زیر، خط تیره و یا اسلش باشد."

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr "مثال: 'about/contact/'. مطمئن شوید که یک اسلش در ابتدا وجود دارد. "

msgid "URL is missing a leading slash."
msgstr "در آدرس اسلش آغازین فراموش شده است."

msgid "URL is missing a trailing slash."
msgstr "در آدرس اسلش پایانی فراموش شده است."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "صفحه تخت با آدرس %(url)s برای سایت %(site)s وجود دارد "

msgid "title"
msgstr "عنوان"

msgid "content"
msgstr "محتوا"

msgid "enable comments"
msgstr "فعال کردن نظرات"

msgid "template name"
msgstr "نام قالب"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"مثال: 'flatpages/contact_page.html'. اگر این مشخص نشود، سیستم از 'flatpages/"
"default.html' استفاده خواهد کرد."

msgid "registration required"
msgstr "عضویت لازم است"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""
"اگر این انتخاب شود، فقط کاربران وارد شده خواهند توانست این صفحه را مشاهده "
"نمایند."

msgid "sites"
msgstr "وب‌گاه‌ها"

msgid "flat page"
msgstr "صفحه تخت"

msgid "flat pages"
msgstr "صفحات تخت"
