# This file is distributed under the same license as the Django package.
#
# Translators:
# e4db27214f7e7544f2022c647b585925_bb0e321, 2015
# e4db27214f7e7544f2022c647b585925_bb0e321, 2014
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2020-2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-02-11 05:54+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (http://www.transifex.com/django/django/language/"
"es/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: es\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Redirects"
msgstr "Redirecciones"

msgid "site"
msgstr "sitio"

msgid "redirect from"
msgstr "redireccionar desde"

msgid ""
"This should be an absolute path, excluding the domain name. Example: “/"
"events/search/”."
msgstr ""
"Esta debe ser una ruta absoluta, excluyendo el nombre de dominio. Ejemplo: “/"
"eventos/búsqueda/”."

msgid "redirect to"
msgstr "redireccionar a"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with a "
"scheme such as “https://”."
msgstr ""
"Esto puede ser una ruta absoluta (como se indica arriba) o una URL completa "
"que comience con un esquema como \"https: //\"."

msgid "redirect"
msgstr "redirección"

msgid "redirects"
msgstr "redirecciones"
