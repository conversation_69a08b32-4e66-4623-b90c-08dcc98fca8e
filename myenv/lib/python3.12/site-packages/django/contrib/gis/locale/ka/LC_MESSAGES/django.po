# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2013,2015
# <AUTHOR> <EMAIL>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-01-19 16:49+0100\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Georgian (http://www.transifex.com/django/django/language/"
"ka/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ka\n"
"Plural-Forms: nplurals=2; plural=(n!=1);\n"

msgid "GIS"
msgstr ""

msgid "The base GIS field."
msgstr ""

msgid ""
"The base Geometry field -- maps to the OpenGIS Specification Geometry type."
msgstr ""

msgid "Point"
msgstr "წერტილი"

msgid "Line string"
msgstr "ხაზის მასივი"

msgid "Polygon"
msgstr "მრავალკუთხედი"

msgid "Multi-point"
msgstr "წერტილების სიმრავლე"

msgid "Multi-line string"
msgstr "ხაზების სიმრავლის მასივი"

msgid "Multi polygon"
msgstr "მრავალკუთხედების სიმრავლე"

msgid "Geometry collection"
msgstr "გეომეტრიული კოლექცია"

msgid "Extent Aggregate Field"
msgstr ""

msgid "Raster Field"
msgstr ""

msgid "No geometry value provided."
msgstr "გეომეტრიის მნიშვნელობა მოცემული არ არის."

msgid "Invalid geometry value."
msgstr "გეომეტრიის მნიშვნელობა არასწორია."

msgid "Invalid geometry type."
msgstr "გეომეტრიის ტიპი არასწორია."

msgid ""
"An error occurred when transforming the geometry to the SRID of the geometry "
"form field."
msgstr "ველიდან გეომეტრიის SRID-ში გადაყვანისას მოხდა შეცდომა."

msgid "Delete all Features"
msgstr ""

msgid "WKT debugging window:"
msgstr ""

msgid "Debugging window (serialized value)"
msgstr ""

msgid "No feeds are registered."
msgstr "არცერთი ფიდი არ არის რეგისტრირებული."

#, python-format
msgid "Slug %r isn't registered."
msgstr "სლაგი %r არ არის რეგისტრირებული."
