# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2011
# <AUTHOR> <EMAIL>, 2019,2021
# <AUTHOR> <EMAIL>, 2016
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-01-25 22:49+0000\n"
"Last-Translator: NullIsNot0 <<EMAIL>>\n"
"Language-Team: Latvian (http://www.transifex.com/django/django/language/"
"lv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: lv\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n != 0 ? 1 : "
"2);\n"

msgid "Redirects"
msgstr "Pārvirzīšanas"

msgid "site"
msgstr "vietne"

msgid "redirect from"
msgstr "pārvirzīt(redirect) no"

msgid ""
"This should be an absolute path, excluding the domain name. Example: “/"
"events/search/”."
msgstr ""
"Tam jābūt absolūtajam ceļam, atskaitot domēna vārdu. Piemēram: \"/notikumi/"
"meklet/\"."

msgid "redirect to"
msgstr "pārvirzīt(redirect) uz"

msgid ""
"This can be either an absolute path (as above) or a full URL starting with a "
"scheme such as “https://”."
msgstr ""
"Šis var būt vai nu absolūtais ceļš (kā augstāk), vai pilnais URL, kas sākas "
"ar \"https://\"."

msgid "redirect"
msgstr "pārvirzīt"

msgid "redirects"
msgstr "pārvirzījumi"
