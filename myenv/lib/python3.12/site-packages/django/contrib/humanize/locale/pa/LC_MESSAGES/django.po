# This file is distributed under the same license as the Django package.
#
# Translators:
# A <PERSON> <<EMAIL>>, 2013
# <PERSON><PERSON> <<EMAIL>>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON><PERSON> (Punjabi) (http://www.transifex.com/django/django/"
"language/pa/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: pa\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Humanize"
msgstr ""

msgid "th"
msgstr "ਵਾਂ"

msgid "st"
msgstr "ਲਾਂ"

msgid "nd"
msgstr "ਜਾ"

msgid "rd"
msgstr "ਜਾ"

#, python-format
msgid "%(value).1f million"
msgid_plural "%(value).1f million"
msgstr[0] "%(value).1f ਮਿਲੀਅਨ"
msgstr[1] "%(value).1f ਮਿਲੀਅਨ"

#, python-format
msgid "%(value)s million"
msgid_plural "%(value)s million"
msgstr[0] "%(value)s ਮਿਲੀਅਨ"
msgstr[1] "%(value)s ਮਿਲੀਅਨ"

#, python-format
msgid "%(value).1f billion"
msgid_plural "%(value).1f billion"
msgstr[0] "%(value).1f ਬਿਲੀਅਨ"
msgstr[1] "%(value).1f ਬਿਲੀਅਨ"

#, python-format
msgid "%(value)s billion"
msgid_plural "%(value)s billion"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "%(value).1f trillion"
msgid_plural "%(value).1f trillion"
msgstr[0] "%(value).1f ਟਰਲੀਅਨ"
msgstr[1] "%(value).1f ਟਰਲੀਅਨ"

#, python-format
msgid "%(value)s trillion"
msgid_plural "%(value)s trillion"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "%(value).1f quadrillion"
msgid_plural "%(value).1f quadrillion"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "%(value)s quadrillion"
msgid_plural "%(value)s quadrillion"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "%(value).1f quintillion"
msgid_plural "%(value).1f quintillion"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "%(value)s quintillion"
msgid_plural "%(value)s quintillion"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "%(value).1f sextillion"
msgid_plural "%(value).1f sextillion"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "%(value)s sextillion"
msgid_plural "%(value)s sextillion"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "%(value).1f septillion"
msgid_plural "%(value).1f septillion"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "%(value)s septillion"
msgid_plural "%(value)s septillion"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "%(value).1f octillion"
msgid_plural "%(value).1f octillion"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "%(value)s octillion"
msgid_plural "%(value)s octillion"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "%(value).1f nonillion"
msgid_plural "%(value).1f nonillion"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "%(value)s nonillion"
msgid_plural "%(value)s nonillion"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "%(value).1f decillion"
msgid_plural "%(value).1f decillion"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "%(value)s decillion"
msgid_plural "%(value)s decillion"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "%(value).1f googol"
msgid_plural "%(value).1f googol"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgid "%(value)s googol"
msgid_plural "%(value)s googol"
msgstr[0] ""
msgstr[1] ""

msgid "one"
msgstr "ਇੱਕ"

msgid "two"
msgstr "ਦੋ"

msgid "three"
msgstr "ਤਿੰਨ"

msgid "four"
msgstr "ਚਾਰ"

msgid "five"
msgstr "ਪੰਜ"

msgid "six"
msgstr "ਛੇ"

msgid "seven"
msgstr "ਸੱਤ"

msgid "eight"
msgstr "ਅੱਠ"

msgid "nine"
msgstr "ਨੌ"

msgid "today"
msgstr "ਅੱਜ"

msgid "tomorrow"
msgstr "ਭਲਕੇ"

msgid "yesterday"
msgstr "ਕੱਲ੍ਹ"

#, python-format
msgctxt "naturaltime"
msgid "%(delta)s ago"
msgstr ""

msgid "now"
msgstr "ਹੁਣ"

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "a second ago"
msgid_plural "%(count)s seconds ago"
msgstr[0] ""
msgstr[1] ""

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "a minute ago"
msgid_plural "%(count)s minutes ago"
msgstr[0] ""
msgstr[1] ""

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "an hour ago"
msgid_plural "%(count)s hours ago"
msgstr[0] ""
msgstr[1] ""

#, python-format
msgctxt "naturaltime"
msgid "%(delta)s from now"
msgstr ""

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "a second from now"
msgid_plural "%(count)s seconds from now"
msgstr[0] ""
msgstr[1] ""

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "a minute from now"
msgid_plural "%(count)s minutes from now"
msgstr[0] ""
msgstr[1] ""

#. Translators: please keep a non-breaking space (U+00A0)
#. between count and time unit.
#, python-format
msgid "an hour from now"
msgid_plural "%(count)s hours from now"
msgstr[0] ""
msgstr[1] ""
