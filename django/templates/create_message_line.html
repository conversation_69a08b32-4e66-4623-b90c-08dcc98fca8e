{% extends "layout/base.html" %}
{% load static %}

{% block content %}
<div class="container py-4">
  <div class="row g-4">
    <!-- Form -->
    <div class="col-lg-8">
      <div class="card shadow-sm p-4">
        <form method="post" action="{% url 'store_message_line' %}">
          {% csrf_token %}
          <div class="mb-3">
            <label class="form-label">取引先コード (複数ある場合はカンマ区切り)</label>
            <input type="text"  name="customer_codes" class="form-control" value="10001, 10002">
          </div>

          <div class="mb-3">
            <label class="form-label">配信日付</label>
            <input id="delivery_datetime" type="text" class="form-control" value="2025-08-15T20:00">
          </div>

          <div class="btn-group w-20 mb-3" role="group" aria-label="選択">
            <input type="radio" class="btn-check" name="format_type" id="option1" autocomplete="off" checked value="商品選択">
            <label class="btn btn-outline-secondary select_product"  for="option1">商品選択</label>

            <input type="radio" class="btn-check" name="format_type" id="option2" autocomplete="off" value="カスタム">
            <label class="btn btn-outline-secondary select_product"  for="option2">カスタム</label>
          </div>

          <div class="row mb-3">
            <div class="col-9">
              <input
                      id="product-code-input"
                      name="product_code"
                      autocomplete="off"
                      class="form-control"
                      list="datalistOptions"
                      placeholder="商品コードを入力する"
                      value="{{ product_code }}"
              />
              <datalist id="datalistOptions">
                {% for product in products %}
                <option value="{{product.product_code}}"></option>
                {% endfor %}
              </datalist>
            </div>
            <div class="col-3">
              <button id="get-product-info" class="btn btn-outline-danger w-100" type="button">情報取得</button>
            </div>
          </div>
          <div class="mb-3">
            <label class="form-label d-block">画像(画像変更する場合は画像をクリックしてください)</label>
            <img width="400" height="200" id="product-image"  src="{% static 'image/preview_default.png' %}" alt="preview" class="preview-image img-fluid rounded">
          </div>

          <div class="mb-3">
            <label class="form-label">タイトル</label>
            <input id="product-title" type="text" class="form-control" value="切目入りウインナー 東亜 1kg">
          </div>

          <div class="mb-3">
            <label class="form-label">申出し</label>
            <input id="product-price" type="text" class="form-control" value="¥1,000">
          </div>

          <div class="mb-3">
            <label class="form-label">テキスト</label>
            <textarea id="product-description" class="form-control" rows="2">規格：1L / 入数：12 / 単位：本 / 最短納品予定日：4営業日 / 発注単位：バラ/ケース</textarea>
          </div>

          <div class="mb-3">
            <label class="form-label">遷移先URL</label>
            <input type="text" class="form-control" value="自動生成">
          </div>
          <div>
            <button type="submit" class="btn btn-primary  w-100">送信</button>
          </div>
        </form>
      </div>
    </div>

    <!-- Preview -->
    <div class="col-lg-4">
      <h6 class="mb-2">プレビュー</h6>
      <div class="card preview-card shadow-sm">
        <img  class="preview-image" id="preview-image" src="{% static 'image/preview_default.png' %}" alt="商品画像">
        <div class="card-body">
          <h5 id="preview-title" class="card-title">切目入りウインナー 東亜...</h5>
          <p id="preview-price" class="card-text text-muted">¥1,000</p>
          <p id="preview-description" class="small text-muted">規格：1L / 入数：12 / 単位：本 / 最短納品予定日：4営業日 / 発注単位：バラ/ケース</p>
          <a href="#" class="btn btn-primary w-100">商品を見る</a>
        </div>
      </div>
    </div>
  </div>
{% endblock %}
{% block extra_js%}
<script>
  $(document).ready(function () {
    flatpickr("#delivery_datetime", {
      enableTime: true,                // bật chọn giờ
      dateFormat: "Y-m-d H:i:S",       // format theo Django
      time_24hr: true,                 // giờ 24h
      enableSeconds: true,          // bật giây
      allowInput: true,
      locale: "ja",
    });

    // AJAX call to get product information
    $('#get-product-info').click(function() {
      const productCode = $('#product-code-input').val().trim();

      if (!productCode) {
        alert('商品コードを入力してください');
        return;
      }

      // Show loading state
      $(this).prop('disabled', true).text('取得中...');

      $.ajax({
        url: `/api/products/${productCode}/`,
        type: 'GET',
        success: function(data) {
          // Populate form fields with product data
          $('#product-title').val(data.product_name || '');
          $('#product-description').val(generateProductDescription(data));

          // Update preview
          $('#preview-title').text(data.product_name || '');
          $('#product-price').text('');
          $('#preview-description').text(generateProductDescription(data));
          // Update preview image
          if (data.image !=='') {
            checkImage(data.image, function(exists) {
                if (exists) {
                   $('.preview-image').attr('src', data.image);
                }
            });
          } else {
            $('.preview-image ').attr('src', '/static/image/preview_default.png');
          }
        },
        error: function(xhr, status, error) {
          if (xhr.status === 404) {
            alert('商品が見つかりませんでした');
          } else {
            alert('エラーが発生しました: ' + error);
          }
          $('#product-title').val( '');
          $('#product-description').val('');

          // Update preview
          $('#preview-title').text('');
          $('#preview-description').text('');
        },
        complete: function() {
          // Reset button state
          $('#get-product-info').prop('disabled', false).text('情報取得');
        }
      });
    });

    // Function to generate product description from product data
    function generateProductDescription(data) {
      let description = '';
      if (data.product_standard) description += `規格：${data.product_standard} / `;
      if (data.in_numbers) description += `入数：${data.in_numbers} / `;
      if (data.unit_name) description += `単位：${data.unit_name} / `;
      if (data.lead_time) description += `最短納品予定日：${data.lead_time}営業日 / `;
      description += '発注単位：バラ/ケース';
      return description;
    }
    function checkImage(url, callback) {
    var img = new Image();
    img.onload = function() {
        callback(true);   // Ảnh tồn tại và load được
    };
    img.onerror = function() {
        callback(false);  // Link lỗi / không load được
    };
    img.src = url;
}
  });
</script>
{% endblock %}