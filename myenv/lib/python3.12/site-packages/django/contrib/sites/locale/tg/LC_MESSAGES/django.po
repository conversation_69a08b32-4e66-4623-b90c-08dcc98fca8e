# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2020
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2020-05-15 00:35+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Tajik (http://www.transifex.com/django/django/language/tg/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: tg\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Sites"
msgstr "Сомонаҳо"

msgid "The domain name cannot contain any spaces or tabs."
msgstr "Номи домен наметавонад аз фосилаҳо ва табулятсия иборат бошад."

msgid "domain name"
msgstr "номи доменӣ"

msgid "display name"
msgstr "номи инъикосшуда"

msgid "site"
msgstr "сомона"

msgid "sites"
msgstr "сомонаҳо"
