# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <inactive+<PERSON>@transifex.com>, 2011
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2012,2014,2019
# <PERSON><PERSON> <<EMAIL>>, 2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-04-07 14:40+0200\n"
"PO-Revision-Date: 2021-09-22 07:22+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Hebrew (http://www.transifex.com/django/django/language/he/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: he\n"
"Plural-Forms: nplurals=4; plural=(n == 1 && n % 1 == 0) ? 0 : (n == 2 && n % "
"1 == 0) ? 1: (n % 10 == 0 && n % 1 == 0 && n > 10) ? 2 : 3;\n"

msgid "Humanize"
msgstr "האנשה"

#. Translators: Ordinal format for 11 (11th), 12 (12th), and 13 (13th).
msgctxt "ordinal 11, 12, 13"
msgid "{}th"
msgstr "ה־{}"

#. Translators: Ordinal format when value ends with 0, e.g. 80th.
msgctxt "ordinal 0"
msgid "{}th"
msgstr "ה־{}"

#. Translators: Ordinal format when value ends with 1, e.g. 81st, except 11.
msgctxt "ordinal 1"
msgid "{}st"
msgstr "ה־{}"

#. Translators: Ordinal format when value ends with 2, e.g. 82nd, except 12.
msgctxt "ordinal 2"
msgid "{}nd"
msgstr "ה־{}"

#. Translators: Ordinal format when value ends with 3, e.g. 83th, except 13.
msgctxt "ordinal 3"
msgid "{}rd"
msgstr "ה־{}"

#. Translators: Ordinal format when value ends with 4, e.g. 84th.
msgctxt "ordinal 4"
msgid "{}th"
msgstr "ה־{}"

#. Translators: Ordinal format when value ends with 5, e.g. 85th.
msgctxt "ordinal 5"
msgid "{}th"
msgstr "ה־{}"

#. Translators: Ordinal format when value ends with 6, e.g. 86th.
msgctxt "ordinal 6"
msgid "{}th"
msgstr "ה־{}"

#. Translators: Ordinal format when value ends with 7, e.g. 87th.
msgctxt "ordinal 7"
msgid "{}th"
msgstr "ה־{}"

#. Translators: Ordinal format when value ends with 8, e.g. 88th.
msgctxt "ordinal 8"
msgid "{}th"
msgstr "ה־{}"

#. Translators: Ordinal format when value ends with 9, e.g. 89th.
msgctxt "ordinal 9"
msgid "{}th"
msgstr "ה־{}"

#, python-format
msgid "%(value)s million"
msgid_plural "%(value)s million"
msgstr[0] "מיליון"
msgstr[1] "%(value)s מיליון"
msgstr[2] "%(value)s מיליון"
msgstr[3] "%(value)s מיליון"

#, python-format
msgid "%(value)s billion"
msgid_plural "%(value)s billion"
msgstr[0] "מיליארד"
msgstr[1] "%(value)s מיליארד"
msgstr[2] "%(value)s מיליארד"
msgstr[3] "%(value)s מיליארד"

#, python-format
msgid "%(value)s trillion"
msgid_plural "%(value)s trillion"
msgstr[0] "טריליון"
msgstr[1] "%(value)s טריליון"
msgstr[2] "%(value)s טריליון"
msgstr[3] "%(value)s טריליון"

#, python-format
msgid "%(value)s quadrillion"
msgid_plural "%(value)s quadrillion"
msgstr[0] "קוודריליון"
msgstr[1] "%(value)s קוודריליון"
msgstr[2] "%(value)s קוודריליון"
msgstr[3] "%(value)s קוודריליון"

#, python-format
msgid "%(value)s quintillion"
msgid_plural "%(value)s quintillion"
msgstr[0] "קווינטיליון"
msgstr[1] "%(value)s קווינטיליון"
msgstr[2] "%(value)s קווינטיליון"
msgstr[3] "%(value)s קווינטיליון"

#, python-format
msgid "%(value)s sextillion"
msgid_plural "%(value)s sextillion"
msgstr[0] "סקסטיליון"
msgstr[1] "%(value)s סקסטיליון"
msgstr[2] "%(value)s סקסטיליון"
msgstr[3] "%(value)s סקסטיליון"

#, python-format
msgid "%(value)s septillion"
msgid_plural "%(value)s septillion"
msgstr[0] "ספטיליון"
msgstr[1] "%(value)s ספטיליון"
msgstr[2] "%(value)s ספטיליון"
msgstr[3] "%(value)s ספטיליון"

#, python-format
msgid "%(value)s octillion"
msgid_plural "%(value)s octillion"
msgstr[0] "אוקטיליון"
msgstr[1] "%(value)s אוקטיליון"
msgstr[2] "%(value)s אוקטיליון"
msgstr[3] "%(value)s אוקטיליון"

#, python-format
msgid "%(value)s nonillion"
msgid_plural "%(value)s nonillion"
msgstr[0] "נוניליון"
msgstr[1] "%(value)s נוניליון"
msgstr[2] "%(value)s נוניליון"
msgstr[3] "%(value)s נוניליון"

#, python-format
msgid "%(value)s decillion"
msgid_plural "%(value)s decillion"
msgstr[0] "דציליון"
msgstr[1] "%(value)s דציליון"
msgstr[2] "%(value)s דציליון"
msgstr[3] "%(value)s דציליון"

#, python-format
msgid "%(value)s googol"
msgid_plural "%(value)s googol"
msgstr[0] "גוגול"
msgstr[1] "%(value)s גוגול"
msgstr[2] "%(value)s גוגול"
msgstr[3] "%(value)s גוגול"

msgid "one"
msgstr "אחד"

msgid "two"
msgstr "שניים"

msgid "three"
msgstr "שלושה"

msgid "four"
msgstr "ארבעה"

msgid "five"
msgstr "חמישה"

msgid "six"
msgstr "שישה"

msgid "seven"
msgstr "שבעה"

msgid "eight"
msgstr "שמונה"

msgid "nine"
msgstr "תשעה"

msgid "today"
msgstr "היום"

msgid "tomorrow"
msgstr "מחר"

msgid "yesterday"
msgstr "אתמול"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s ago"
msgstr "לפני %(delta)s"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour ago"
msgid_plural "%(count)s hours ago"
msgstr[0] "לפני שעה"
msgstr[1] "לפני שעתיים"
msgstr[2] "לפני %(count)s שעות"
msgstr[3] "לפני %(count)s שעות"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute ago"
msgid_plural "%(count)s minutes ago"
msgstr[0] "לפני דקה"
msgstr[1] "לפני %(count)s דקות"
msgstr[2] "לפני %(count)s דקות"
msgstr[3] "לפני %(count)s דקות"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second ago"
msgid_plural "%(count)s seconds ago"
msgstr[0] "לפני שנייה"
msgstr[1] "לפני %(count)s שניות"
msgstr[2] "לפני %(count)s שניות"
msgstr[3] "לפני %(count)s שניות"

msgid "now"
msgstr "עכשיו"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second from now"
msgid_plural "%(count)s seconds from now"
msgstr[0] "בעוד שנייה"
msgstr[1] "בעוד %(count)s שניות"
msgstr[2] "בעוד %(count)s שניות"
msgstr[3] "בעוד %(count)s שניות"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute from now"
msgid_plural "%(count)s minutes from now"
msgstr[0] "בעוד דקה"
msgstr[1] "בעוד %(count)s דקות"
msgstr[2] "בעוד %(count)s דקות"
msgstr[3] "בעוד %(count)s דקות"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour from now"
msgid_plural "%(count)s hours from now"
msgstr[0] "בעוד שעה"
msgstr[1] "בעוד שעתיים"
msgstr[2] "בעוד %(count)s שעות"
msgstr[3] "בעוד %(count)s שעות"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s from now"
msgstr "בעוד %(delta)s"

#. Translators: 'naturaltime-past' strings will be included in '%(delta)s ago'
#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "שנה"
msgstr[1] "שנתיים"
msgstr[2] "%(num)d שנים"
msgstr[3] "%(num)d שנים"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "חודש"
msgstr[1] "חודשיים"
msgstr[2] "%(num)d חודשים"
msgstr[3] "%(num)d חודשים"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "שבוע"
msgstr[1] "שבועיים"
msgstr[2] "%(num)d שבועות"
msgstr[3] "%(num)d שבועות"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "יום"
msgstr[1] "יומיים"
msgstr[2] "%(num)d ימים"
msgstr[3] "%(num)d ימים"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "שעה"
msgstr[1] "שעתיים"
msgstr[2] "%(num)d שעות"
msgstr[3] "%(num)d שעות"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "דקה"
msgstr[1] "%(num)d דקות"
msgstr[2] "%(num)d דקות"
msgstr[3] "%(num)d דקות"

#. Translators: 'naturaltime-future' strings will be included in '%(delta)s
#. from now'
#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "שנה"
msgstr[1] "שנתיים"
msgstr[2] "%(num)d שנים"
msgstr[3] "%(num)d שנים"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "חודש"
msgstr[1] "חודשיים"
msgstr[2] "%(num)d חודשים"
msgstr[3] "%(num)d חודשים"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "שבוע"
msgstr[1] "שבועיים"
msgstr[2] "%(num)d שבועות"
msgstr[3] "%(num)d שבועות"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "יום"
msgstr[1] "יומיים"
msgstr[2] "%(num)d ימים"
msgstr[3] "%(num)d ימים"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "שעה"
msgstr[1] "שעתיים"
msgstr[2] "%(num)d שעות"
msgstr[3] "%(num)d שעות"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "דקה"
msgstr[1] "%(num)d דקות"
msgstr[2] "%(num)d דקות"
msgstr[3] "%(num)d דקות"
