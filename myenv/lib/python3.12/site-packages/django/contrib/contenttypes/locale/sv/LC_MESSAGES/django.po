# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <PERSON> <<EMAIL>>, 2012
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON>, 2014
# <PERSON>, 2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2022-07-24 19:22+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Swedish (http://www.transifex.com/django/django/language/"
"sv/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: sv\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr "Innehållstyper"

msgid "python model class name"
msgstr "klassnamn för Python-modell"

msgid "content type"
msgstr "innehållstyp"

msgid "content types"
msgstr "innehållstyper"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Innehållstypens %(ct_id)s har ingen associerad modell."

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "Innehållstypens %(ct_id)s objekt %(obj_id)s finns inte"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "%(ct_name)s objekt har inte en get_absolute_url()-metod"
