# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2019
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2019-03-03 09:57+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Malayalam (http://www.transifex.com/django/django/language/"
"ml/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ml\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Sites"
msgstr "സൈറ്റുകൾ"

msgid "The domain name cannot contain any spaces or tabs."
msgstr "ഡൊമൈനിന്റെ പേരിൽ സ്പെയ്സുകളോ ടാബുകളോ അനുവദനീയമല്ല."

msgid "domain name"
msgstr "ഡൊമൈനിന്റെ പേര്"

msgid "display name"
msgstr "കാഴ്ചയ്ക്കായുള്ള പേര്"

msgid "site"
msgstr "സൈറ്റ്"

msgid "sites"
msgstr "സൈറ്റുകള്‍"
