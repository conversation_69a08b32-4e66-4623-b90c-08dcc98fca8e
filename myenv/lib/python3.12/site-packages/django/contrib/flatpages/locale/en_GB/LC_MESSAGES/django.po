# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2012
# <PERSON> <<EMAIL>>, 2011-2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-10-09 17:42+0200\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: English (United Kingdom) (http://www.transifex.com/django/"
"django/language/en_GB/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: en_GB\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr "Advanced options"

msgid "Flat Pages"
msgstr ""

msgid "URL"
msgstr "URL"

msgid ""
"Example: '/about/contact/'. Make sure to have leading and trailing slashes."
msgstr ""
"Example: '/about/contact/'. Make sure to have leading and trailing slashes."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."

msgid "URL is missing a leading slash."
msgstr "URL is missing a leading slash."

msgid "URL is missing a trailing slash."
msgstr "URL is missing a trailing slash."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "Flatpage with url %(url)s already exists for site %(site)s"

msgid "title"
msgstr "title"

msgid "content"
msgstr "content"

msgid "enable comments"
msgstr "enable comments"

msgid "template name"
msgstr "template name"

msgid ""
"Example: 'flatpages/contact_page.html'. If this isn't provided, the system "
"will use 'flatpages/default.html'."
msgstr ""
"Example: 'flatpages/contact_page.html'. If this isn't provided, the system "
"will use 'flatpages/default.html'."

msgid "registration required"
msgstr "registration required"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""
"If this is checked, only logged-in users will be able to view the page."

msgid "sites"
msgstr ""

msgid "flat page"
msgstr "flat page"

msgid "flat pages"
msgstr "flat pages"
