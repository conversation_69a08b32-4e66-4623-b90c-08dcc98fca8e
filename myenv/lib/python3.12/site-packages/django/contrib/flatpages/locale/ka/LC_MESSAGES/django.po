# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2013
# <AUTHOR> <EMAIL>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-10-09 17:42+0200\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Georgian (http://www.transifex.com/django/django/language/"
"ka/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ka\n"
"Plural-Forms: nplurals=2; plural=(n!=1);\n"

msgid "Advanced options"
msgstr "დამატებითი პარამეტრები"

msgid "Flat Pages"
msgstr ""

msgid "URL"
msgstr "URL"

msgid ""
"Example: '/about/contact/'. Make sure to have leading and trailing slashes."
msgstr ""
"მაგალითი: '/about/contact/'. ყურადღება მიაქციეთ დახრილ ხაზებს თავში და "
"ბოლოში."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"ეს მნიშვნელობა უნდა შეიცავდეს მხოლოდ ლათინურ ასოებს, ციფრებს, წერტილებს, "
"ხაზგასმის ნიშნებს, დეფისებს, დახრილ ხაზებს და ტილდებს."

msgid "URL is missing a leading slash."
msgstr "URL-ს დასაწყისში აკლია დახრილი ხაზი."

msgid "URL is missing a trailing slash."
msgstr "URL-ს ბოლოში აკლია დახრილი ხაზი."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "უბრალო გვერდი url-ით %(url)s უკვე არსებობს საიტისთვის %(site)s"

msgid "title"
msgstr "სათაური"

msgid "content"
msgstr "კონტენტი"

msgid "enable comments"
msgstr "ჩავრთოთ კომენტარები"

msgid "template name"
msgstr "შაბლონის სახელი"

msgid ""
"Example: 'flatpages/contact_page.html'. If this isn't provided, the system "
"will use 'flatpages/default.html'."
msgstr ""
"მაგალითი: 'flatpages/contact_page.html'. თუ იგი მითითებული არ არის, "
"გამოყენებული იქნება 'flatpages/default.html'."

msgid "registration required"
msgstr "რეგისტრაცია აუცილებელია"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""
"თუ ეს დროშა ჩართულია, მხოლო შემოსულ მომხმარებლებს ექნებათ გვერდის "
"დათვალიერების საშუალება."

msgid "sites"
msgstr ""

msgid "flat page"
msgstr "უბრალო გვერდი"

msgid "flat pages"
msgstr "უბრალო გვერდები"
