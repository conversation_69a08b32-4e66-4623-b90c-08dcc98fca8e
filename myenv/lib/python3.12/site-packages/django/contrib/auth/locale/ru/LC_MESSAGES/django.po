# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2020
# <PERSON> <<EMAIL>>, 2017
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <AUTHOR> <EMAIL>, 2012-2015,2022-2024
# <AUTHOR> <EMAIL>, 2016
# <AUTHOR> <EMAIL>, 2021
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2016
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-05-22 11:46-0300\n"
"PO-Revision-Date: 2024-08-07 08:09+0000\n"
"Last-Translator: Алексей Борискин <<EMAIL>>, 2012-2015,2022-2024\n"
"Language-Team: Russian (http://app.transifex.com/django/django/language/"
"ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || "
"(n%100>=11 && n%100<=14)? 2 : 3);\n"

msgid "Personal info"
msgstr "Персональная информация"

msgid "Permissions"
msgstr "Права доступа"

msgid "Important dates"
msgstr "Важные даты"

#, python-format
msgid "%(name)s object with primary key %(key)r does not exist."
msgstr "%(name)s с первичным ключом %(key)r не существует."

msgid "Conflicting form data submitted. Please try again."
msgstr ""
"Отправлены взаимоисключающие данные. Измените данные и попробуйте снова."

msgid "Password changed successfully."
msgstr "Пароль успешно изменен."

msgid "Password-based authentication was disabled."
msgstr "Аутентификация по паролю была запрещена."

#, python-format
msgid "Change password: %s"
msgstr "Изменить пароль: %s"

#, python-format
msgid "Set password: %s"
msgstr "Задать пароль: %s"

msgid "Authentication and Authorization"
msgstr "Пользователи и группы"

msgid "password"
msgstr "пароль"

msgid "last login"
msgstr "последний вход"

msgid "No password set."
msgstr "Пароль не задан."

msgid "Invalid password format or unknown hashing algorithm."
msgstr "Неизвестный формат пароля или алгоритм хеширования."

msgid "Reset password"
msgstr "Сбросить пароль"

msgid "Set password"
msgstr "Задать пароль"

msgid "The two password fields didn’t match."
msgstr "Введенные пароли не совпадают."

msgid ""
"Whether the user will be able to authenticate using a password or not. If "
"disabled, they may still be able to authenticate using other backends, such "
"as Single Sign-On or LDAP."
msgstr ""
"Может ли пользователь аутентифироваться по паролю. Если эта возможность "
"выключена, пользователь всё ещё может аутентифицироваться иными способами, "
"например, Single Sing-On или LDAP, если они сконфигурированы и разрешены."

msgid "Password"
msgstr "Пароль"

msgid "Password confirmation"
msgstr "Подтверждение пароля"

msgid "Enter the same password as before, for verification."
msgstr "Для подтверждения введите, пожалуйста, пароль ещё раз."

msgid "Password-based authentication"
msgstr "Аутентификация по паролю"

msgid "Enabled"
msgstr "Разрешена"

msgid "Disabled"
msgstr "Запрещена"

msgid ""
"Raw passwords are not stored, so there is no way to see the user’s password."
msgstr ""
"Мы не храним сырые пароли, поэтому не существует способа посмотреть пароль "
"пользователя."

msgid ""
"Enable password-based authentication for this user by setting a password."
msgstr ""
"Разрешить аутентификацию по паролю для этого пользователя, задав его пароль."

#, python-format
msgid ""
"Please enter a correct %(username)s and password. Note that both fields may "
"be case-sensitive."
msgstr ""
"Пожалуйста, введите правильные %(username)s и пароль. Оба поля могут быть "
"чувствительны к регистру."

msgid "This account is inactive."
msgstr "Эта учетная запись отключена."

msgid "Email"
msgstr "Адрес электронной почты"

msgid "New password"
msgstr "Новый пароль"

msgid "New password confirmation"
msgstr "Подтверждение нового пароля"

msgid "Your old password was entered incorrectly. Please enter it again."
msgstr "Ваш старый пароль введен неправильно. Пожалуйста, введите его снова."

msgid "Old password"
msgstr "Старый пароль"

msgid "algorithm"
msgstr "алгоритм"

msgid "iterations"
msgstr "итерации"

msgid "salt"
msgstr "соль"

msgid "hash"
msgstr "хэш"

msgid "variety"
msgstr "множество"

msgid "version"
msgstr "версия"

msgid "memory cost"
msgstr "затраты памяти"

msgid "time cost"
msgstr "затраты времени"

msgid "parallelism"
msgstr "параллелизм"

msgid "work factor"
msgstr "рабочий фактор"

msgid "checksum"
msgstr "контрольная сумма"

msgid "block size"
msgstr "размер блока"

msgid "name"
msgstr "имя"

msgid "content type"
msgstr "тип содержимого"

msgid "codename"
msgstr "кодовое название"

msgid "permission"
msgstr "право"

msgid "permissions"
msgstr "права"

msgid "group"
msgstr "группа"

msgid "groups"
msgstr "группы"

msgid "superuser status"
msgstr "статус суперпользователя"

msgid ""
"Designates that this user has all permissions without explicitly assigning "
"them."
msgstr "Указывает, что пользователь имеет все права без явного их назначения."

msgid ""
"The groups this user belongs to. A user will get all permissions granted to "
"each of their groups."
msgstr ""
"Группы, к которым принадлежит данный пользователь. Пользователь получит все "
"права, указанные в каждой из его/её групп."

msgid "user permissions"
msgstr "права пользователя"

msgid "Specific permissions for this user."
msgstr "Индивидуальные права данного пользователя."

msgid "username"
msgstr "имя пользователя"

msgid "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only."
msgstr ""
"Обязательное поле. Не более 150 символов. Только буквы, цифры и символы @/./"
"+/-/_."

msgid "A user with that username already exists."
msgstr "Пользователь с таким именем уже существует."

msgid "first name"
msgstr "имя"

msgid "last name"
msgstr "фамилия"

msgid "email address"
msgstr "адрес электронной почты"

msgid "staff status"
msgstr "статус персонала"

msgid "Designates whether the user can log into this admin site."
msgstr ""
"Отметьте, если пользователь может входить в административную часть сайта."

msgid "active"
msgstr "активный"

msgid ""
"Designates whether this user should be treated as active. Unselect this "
"instead of deleting accounts."
msgstr ""
"Отметьте, если пользователь должен считаться активным. Уберите эту отметку "
"вместо удаления учётной записи."

msgid "date joined"
msgstr "дата регистрации"

msgid "user"
msgstr "пользователь"

msgid "users"
msgstr "пользователи"

#, python-format
msgid ""
"This password is too short. It must contain at least %(min_length)d "
"character."
msgid_plural ""
"This password is too short. It must contain at least %(min_length)d "
"characters."
msgstr[0] ""
"Введённый пароль слишком короткий. Он должен содержать как минимум "
"%(min_length)d символ."
msgstr[1] ""
"Введённый пароль слишком короткий. Он должен содержать как минимум "
"%(min_length)d символа."
msgstr[2] ""
"Введённый пароль слишком короткий. Он должен содержать как минимум "
"%(min_length)d символов."
msgstr[3] ""
"Введённый пароль слишком короткий. Он должен содержать как минимум "
"%(min_length)d символов."

#, python-format
msgid "Your password must contain at least %(min_length)d character."
msgid_plural "Your password must contain at least %(min_length)d characters."
msgstr[0] "Ваш пароль должен содержать как минимум %(min_length)d символ."
msgstr[1] "Ваш пароль должен содержать как минимум %(min_length)d символа."
msgstr[2] "Ваш пароль должен содержать как минимум %(min_length)d символов."
msgstr[3] "Ваш пароль должен содержать как минимум %(min_length)d символов."

#, python-format
msgid "The password is too similar to the %(verbose_name)s."
msgstr "Введённый пароль слишком похож на %(verbose_name)s."

msgid "Your password can’t be too similar to your other personal information."
msgstr "Пароль не должен быть слишком похож на другую вашу личную информацию."

msgid "This password is too common."
msgstr "Введённый пароль слишком широко распространён."

msgid "Your password can’t be a commonly used password."
msgstr "Пароль не должен быть слишком простым и распространенным."

msgid "This password is entirely numeric."
msgstr "Введённый пароль состоит только из цифр."

msgid "Your password can’t be entirely numeric."
msgstr "Пароль не может состоять только из цифр."

#, python-format
msgid "Password reset on %(site_name)s"
msgstr "Сброс пароля на %(site_name)s"

msgid ""
"Enter a valid username. This value may contain only unaccented lowercase a-z "
"and uppercase A-Z letters, numbers, and @/./+/-/_ characters."
msgstr ""
"Введите правильное имя пользователя. Это значение может содержать только "
"строчные буквы латиницы a-z без умляутов, прописные буквы A-Z, цифры и "
"символы @/./+/-/_."

msgid ""
"Enter a valid username. This value may contain only letters, numbers, and "
"@/./+/-/_ characters."
msgstr ""
"Введите правильное имя пользователя. Оно может содержать только буквы, цифры "
"и знаки @/./+/-/_."

msgid "Logged out"
msgstr "Не авторизован"

msgid "Password reset"
msgstr "Восстановление пароля"

msgid "Password reset sent"
msgstr "Письмо с инструкциями по восстановлению пароля отправлено"

msgid "Enter new password"
msgstr "Введите новый пароль"

msgid "Password reset unsuccessful"
msgstr "Ошибка восстановления пароля"

msgid "Password reset complete"
msgstr "Восстановление пароля завершено"

msgid "Password change"
msgstr "Изменение пароля"

msgid "Password change successful"
msgstr "Пароль успешно изменен"
