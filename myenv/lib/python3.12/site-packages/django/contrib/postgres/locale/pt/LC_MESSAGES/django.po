# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2015
# jorgecar<PERSON><PERSON><PERSON> <<EMAIL>>, 2015
# <PERSON><PERSON> <<EMAIL>>, 2015,2017-2018
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2020-05-11 20:56+0200\n"
"PO-Revision-Date: 2020-05-12 20:01+0000\n"
"Last-Translator: Transifex Bot <>\n"
"Language-Team: Portuguese (http://www.transifex.com/django/django/language/"
"pt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: pt\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "PostgreSQL extensions"
msgstr "Extensões de PostgresSQL"

#, python-format
msgid "Item %(nth)s in the array did not validate:"
msgstr "Item %(nth)s na lista não validou:"

msgid "Nested arrays must have the same length."
msgstr "As sub-listas têm de ter o mesmo tamanho."

msgid "Map of strings to strings/nulls"
msgstr "Mapeamento de strings para strings/nulos"

#, python-format
msgid "The value of “%(key)s” is not a string or null."
msgstr ""

msgid "Could not load JSON data."
msgstr "Não foi possível carregar os dados JSON."

msgid "Input must be a JSON dictionary."
msgstr "A entrada deve ser um dicionário JSON."

msgid "Enter two valid values."
msgstr "Introduza dois valores válidos."

msgid "The start of the range must not exceed the end of the range."
msgstr "O início da gama não pode ser maior que o seu fim."

msgid "Enter two whole numbers."
msgstr "Introduza dois números inteiros."

msgid "Enter two numbers."
msgstr "Introduza dois números."

msgid "Enter two valid date/times."
msgstr "Introduza duas datas/horas válidas."

msgid "Enter two valid dates."
msgstr "Introduza duas datas válidas."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no more than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no more than "
"%(limit_value)d."
msgstr[0] ""
"A lista contém %(show_value)d item, não pode conter mais do que "
"%(limit_value)d."
msgstr[1] ""
"A lista contém %(show_value)d itens, não pode conter mais do que "
"%(limit_value)d."

#, python-format
msgid ""
"List contains %(show_value)d item, it should contain no fewer than "
"%(limit_value)d."
msgid_plural ""
"List contains %(show_value)d items, it should contain no fewer than "
"%(limit_value)d."
msgstr[0] ""
"A lista contém %(show_value)d item, tem de conter pelo menos %(limit_value)d."
msgstr[1] ""
"A lista contém %(show_value)d itens, tem de conter pelo menos "
"%(limit_value)d."

#, python-format
msgid "Some keys were missing: %(keys)s"
msgstr "Algumas chaves estão em falta: %(keys)s"

#, python-format
msgid "Some unknown keys were provided: %(keys)s"
msgstr "Foram fornecidas algumas chaves desconhecidas: %(keys)s"

#, python-format
msgid ""
"Ensure that this range is completely less than or equal to %(limit_value)s."
msgstr "Garanta que esta gama é toda ela menor ou igual a %(limit_value)s."

#, python-format
msgid ""
"Ensure that this range is completely greater than or equal to "
"%(limit_value)s."
msgstr "Garanta que esta gama é toda ela maior ou igual a %(limit_value)s."
