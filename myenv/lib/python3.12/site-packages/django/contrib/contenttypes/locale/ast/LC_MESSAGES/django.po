# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2014
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2017-09-20 02:41+0000\n"
"Last-Translator: <PERSON><PERSON> <jann<PERSON>@leidel.info>\n"
"Language-Team: Asturian (http://www.transifex.com/django/django/language/"
"ast/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ast\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr ""

msgid "python model class name"
msgstr "nome de modelu de clas python"

msgid "content type"
msgstr "triba de conteníu"

msgid "content types"
msgstr "tribes de conteníu"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr ""

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn't exist"
msgstr ""

#, python-format
msgid "%(ct_name)s objects don't have a get_absolute_url() method"
msgstr ""
