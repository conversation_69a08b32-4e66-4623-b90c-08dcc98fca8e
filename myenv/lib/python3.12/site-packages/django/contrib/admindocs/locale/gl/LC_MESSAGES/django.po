# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2011,2013
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2013
# <AUTHOR> <EMAIL>, 2023
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2023-04-24 20:19+0000\n"
"Last-Translator: X Bello <<EMAIL>>, 2023\n"
"Language-Team: Galician (http://www.transifex.com/django/django/language/"
"gl/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: gl\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr "Documentación administrativa"

msgid "Home"
msgstr "Inicio"

msgid "Documentation"
msgstr "Documentación"

msgid "Bookmarklets"
msgstr "Bookmarklets"

msgid "Documentation bookmarklets"
msgstr "Bookmarklets de documentación"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Para instalar os bookmarklets, arrastre o enlace á súa barra de marcadores, "
"ou pinche co botón dereito no enlace e engádao ós marcadores. Agora poderá "
"seleccionar o bookmarklet desde calquera páxina do sitio."

msgid "Documentation for this page"
msgstr "Documentación para esta páxina"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr "Salta á documentación para a vista que xera a páxina."

msgid "Tags"
msgstr "Etiquetas"

msgid "List of all the template tags and their functions."
msgstr "Listado de todas as etiquetas de plantilla e as súas funcións."

msgid "Filters"
msgstr "Filtros"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Os filtros son accións que poden aplicarse a variables nunha plantilla para "
"modificar a salida."

msgid "Models"
msgstr "Modelos"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Os modelos son descripcións de tódos os obxetos no sistema e os seus campos "
"asociados. Cada modelo ten unha lista de campos ós que pode accederse como "
"variables de plantilla."

msgid "Views"
msgstr "Vistas"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Cada páxina no sitio público é xerada por unha vista. A vista define qué "
"plantilla se usa para xerar a páxina, e qué obxetos están dispoñibles nesa "
"plantilla."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Ferramentas para o navegador para acceder rápidamente á funcionalidade de "
"administración."

msgid "Please install docutils"
msgstr "Por favor instale docutils"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a "
"href=\"%(link)s\">docutils</a> library."
msgstr ""
"O sistema de documentación de admin require da librería de Python <a "
"href=\"%(link)s\">docutils</a>."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Por favor pídalle ós administradores que instalen <a "
"href=\"%(link)s\">docutils</a>."

#, python-format
msgid "Model: %(name)s"
msgstr "Modelo: %(name)s"

msgid "Fields"
msgstr "Campos"

msgid "Field"
msgstr "Campo"

msgid "Type"
msgstr "Tipo"

msgid "Description"
msgstr "Descrición"

msgid "Methods with arguments"
msgstr "Métodos con argumentos"

msgid "Method"
msgstr "Método"

msgid "Arguments"
msgstr "Argumentos"

msgid "Back to Model documentation"
msgstr "Volver á documentación do Modelo"

msgid "Model documentation"
msgstr "Documentación do modelo"

msgid "Model groups"
msgstr "Modelos dos grupos"

msgid "Templates"
msgstr "Patróns"

#, python-format
msgid "Template: %(name)s"
msgstr "Plantilla: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Plantilla: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Rutas de busca para a plantilla <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(non existe)"

msgid "Back to Documentation"
msgstr "Volver á documentación"

msgid "Template filters"
msgstr "Filtros de plantilla"

msgid "Template filter documentation"
msgstr "Documentación dos filtros de plantilla"

msgid "Built-in filters"
msgstr "Filtros incluidos"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Para usar estes filtros, poña <code>%(code)s</code> na plantilla antes de "
"usar o filtro."

msgid "Template tags"
msgstr "Etiquetas de plantilla"

msgid "Template tag documentation"
msgstr "Documentación das etiquetas de plantilla"

msgid "Built-in tags"
msgstr "Etiquetas  incluidas"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Para usar estas etiquetas, poña <code>%(code)s</code> na plantilla antes de "
"usar a etiqueta."

#, python-format
msgid "View: %(name)s"
msgstr "Vista: %(name)s"

msgid "Context:"
msgstr "Contexto:"

msgid "Templates:"
msgstr "Plantillas:"

msgid "Back to View documentation"
msgstr "Volver á documentación da Vista"

msgid "View documentation"
msgstr "Ver documentación"

msgid "Jump to namespace"
msgstr "Saltar ó espacio de nomes"

msgid "Empty namespace"
msgstr "Baleirar espacio de nomes"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Vistas por espacio de nomes %(name)s"

msgid "Views by empty namespace"
msgstr "Vustas por espacio de nomes baleiro"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"Función de vista: <code>%(full_name)s</code>. Nome <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "etiqueta:"

msgid "filter:"
msgstr "filtro:"

msgid "view:"
msgstr "vista:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Non se atopa a aplicación %(app_label)r"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "O modelo %(model_name)r non se atopou na aplicación %(app_label)r"

msgid "model:"
msgstr "modelo:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "o obxecto relacionado `%(app_label)s.%(data_type)s`"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "obxectos relacionados `%(app_label)s.%(object_name)s`"

#, python-format
msgid "all %s"
msgstr "todos os %s"

#, python-format
msgid "number of %s"
msgstr "número de %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s non semella ser un obxecto urlpattern"
