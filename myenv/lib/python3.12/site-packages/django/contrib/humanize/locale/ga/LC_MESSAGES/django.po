# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON><PERSON>, 2024
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON> <<EMAIL>>, 2019
# <PERSON> <<EMAIL>>, 2011-2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-09-18 11:41-0300\n"
"PO-Revision-Date: 2024-10-07 18:40+0000\n"
"Last-Translator: Ain<PERSON><PERSON><PERSON>, 2024\n"
"Language-Team: Irish (http://app.transifex.com/django/django/language/ga/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ga\n"
"Plural-Forms: nplurals=5; plural=(n==1 ? 0 : n==2 ? 1 : n<7 ? 2 : n<11 ? 3 : "
"4);\n"

msgid "Humanize"
msgstr "<PERSON><PERSON><PERSON>"

#. Translators: Ordinal format for 11 (11th), 12 (12th), and 13 (13th).
msgctxt "ordinal 11, 12, 13"
msgid "{}th"
msgstr "{}ú"

#. Translators: Ordinal format when value ends with 0, e.g. 80th.
msgctxt "ordinal 0"
msgid "{}th"
msgstr "{}ú"

#. Translators: Ordinal format when value ends with 1, e.g. 81st, except 11.
msgctxt "ordinal 1"
msgid "{}st"
msgstr "{}ú"

#. Translators: Ordinal format when value ends with 2, e.g. 82nd, except 12.
msgctxt "ordinal 2"
msgid "{}nd"
msgstr "{}ú"

#. Translators: Ordinal format when value ends with 3, e.g. 83rd, except 13.
msgctxt "ordinal 3"
msgid "{}rd"
msgstr "{}ú"

#. Translators: Ordinal format when value ends with 4, e.g. 84th.
msgctxt "ordinal 4"
msgid "{}th"
msgstr "{}ú"

#. Translators: Ordinal format when value ends with 5, e.g. 85th.
msgctxt "ordinal 5"
msgid "{}th"
msgstr "{}ú"

#. Translators: Ordinal format when value ends with 6, e.g. 86th.
msgctxt "ordinal 6"
msgid "{}th"
msgstr "{}ú"

#. Translators: Ordinal format when value ends with 7, e.g. 87th.
msgctxt "ordinal 7"
msgid "{}th"
msgstr "{}ú"

#. Translators: Ordinal format when value ends with 8, e.g. 88th.
msgctxt "ordinal 8"
msgid "{}th"
msgstr "{}ú"

#. Translators: Ordinal format when value ends with 9, e.g. 89th.
msgctxt "ordinal 9"
msgid "{}th"
msgstr "{}ú"

#, python-format
msgid "%(value)s million"
msgid_plural "%(value)s million"
msgstr[0] " %(value)s  milliún"
msgstr[1] " %(value)s  milliún"
msgstr[2] " %(value)s  milliún"
msgstr[3] " %(value)s  milliún"
msgstr[4] " %(value)s  milliún"

#, python-format
msgid "%(value)s billion"
msgid_plural "%(value)s billion"
msgstr[0] " %(value)s  billiún"
msgstr[1] " %(value)s  billiún"
msgstr[2] " %(value)s  billiún"
msgstr[3] " %(value)s  billiún"
msgstr[4] " %(value)s  billiún"

#, python-format
msgid "%(value)s trillion"
msgid_plural "%(value)s trillion"
msgstr[0] " %(value)s  trilliún"
msgstr[1] " %(value)s  trilliún"
msgstr[2] " %(value)s  trilliún"
msgstr[3] " %(value)s  trilliún"
msgstr[4] " %(value)s  trilliún"

#, python-format
msgid "%(value)s quadrillion"
msgid_plural "%(value)s quadrillion"
msgstr[0] "%(value)s quadrilliún"
msgstr[1] "%(value)s quadrilliún"
msgstr[2] "%(value)s quadrilliún"
msgstr[3] "%(value)s quadrilliún"
msgstr[4] "%(value)s quadrilliún"

#, python-format
msgid "%(value)s quintillion"
msgid_plural "%(value)s quintillion"
msgstr[0] "%(value)s quintillion"
msgstr[1] "%(value)s quintillion"
msgstr[2] "%(value)s quintillion"
msgstr[3] "%(value)s quintillion"
msgstr[4] "%(value)s quintillion"

#, python-format
msgid "%(value)s sextillion"
msgid_plural "%(value)s sextillion"
msgstr[0] "%(value)s sextillion"
msgstr[1] "%(value)s sextillion"
msgstr[2] "%(value)s sextillion"
msgstr[3] "%(value)s sextillion"
msgstr[4] "%(value)s sextillion"

#, python-format
msgid "%(value)s septillion"
msgid_plural "%(value)s septillion"
msgstr[0] "%(value)s septillion"
msgstr[1] "%(value)s septillion"
msgstr[2] "%(value)s septillion"
msgstr[3] "%(value)s septillion"
msgstr[4] "%(value)s septillion"

#, python-format
msgid "%(value)s octillion"
msgid_plural "%(value)s octillion"
msgstr[0] "%(value)s octillion"
msgstr[1] "%(value)s octillion"
msgstr[2] "%(value)s octillion"
msgstr[3] "%(value)s octillion"
msgstr[4] "%(value)s octillion"

#, python-format
msgid "%(value)s nonillion"
msgid_plural "%(value)s nonillion"
msgstr[0] "%(value)s nonillion"
msgstr[1] "%(value)s nonillion"
msgstr[2] "%(value)s nonillion"
msgstr[3] "%(value)s nonillion"
msgstr[4] "%(value)s nonillion"

#, python-format
msgid "%(value)s decillion"
msgid_plural "%(value)s decillion"
msgstr[0] "%(value)s decillion"
msgstr[1] "%(value)s decillion"
msgstr[2] "%(value)s decillion"
msgstr[3] "%(value)s decillion"
msgstr[4] "%(value)s decillion"

#, python-format
msgid "%(value)s googol"
msgid_plural "%(value)s googol"
msgstr[0] "%(value)s googol"
msgstr[1] "%(value)s googol"
msgstr[2] "%(value)s googol"
msgstr[3] "%(value)s googol"
msgstr[4] "%(value)s googol"

msgid "one"
msgstr "aon"

msgid "two"
msgstr "dó"

msgid "three"
msgstr "trí"

msgid "four"
msgstr "ceathair"

msgid "five"
msgstr "cúig"

msgid "six"
msgstr "sé"

msgid "seven"
msgstr "seacht"

msgid "eight"
msgstr "ocht"

msgid "nine"
msgstr "naoi"

msgid "today"
msgstr "inniu"

msgid "tomorrow"
msgstr "amárach"

msgid "yesterday"
msgstr "inné"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s ago"
msgstr "%(delta)s ó shin"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour ago"
msgid_plural "%(count)s hours ago"
msgstr[0] "uair an chloig ó shin"
msgstr[1] "%(count)s uair an chloig ó shin"
msgstr[2] "%(count)s uair an chloig ó shin"
msgstr[3] "%(count)s uair an chloig ó shin"
msgstr[4] "%(count)s uair an chloig ó shin"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute ago"
msgid_plural "%(count)s minutes ago"
msgstr[0] "nóiméad ó shin"
msgstr[1] "%(count)s nóiméad ó shin"
msgstr[2] "%(count)s nóiméad ó shin"
msgstr[3] "%(count)s nóiméad ó shin"
msgstr[4] "%(count)s nóiméad ó shin"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second ago"
msgid_plural "%(count)s seconds ago"
msgstr[0] "soicind ó shin"
msgstr[1] "%(count)s soicindí ó shin"
msgstr[2] "%(count)s soicindí ó shin"
msgstr[3] "%(count)s soicindí ó shin"
msgstr[4] "%(count)s soicindí ó shin"

msgid "now"
msgstr "anois"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second from now"
msgid_plural "%(count)s seconds from now"
msgstr[0] "soicind as seo amach"
msgstr[1] "%(count)s soicind as seo amach"
msgstr[2] "%(count)s soicind as seo amach"
msgstr[3] "%(count)s soicind as seo amach"
msgstr[4] "%(count)s soicind as seo amach"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute from now"
msgid_plural "%(count)s minutes from now"
msgstr[0] "nóiméad ó anois"
msgstr[1] "%(count)s nóiméad as seo"
msgstr[2] "%(count)s nóiméad as seo"
msgstr[3] "%(count)s nóiméad as seo"
msgstr[4] "%(count)s nóiméad as seo"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour from now"
msgid_plural "%(count)s hours from now"
msgstr[0] "uair an chloig ó anois"
msgstr[1] "%(count)s uair an chloig as seo amach"
msgstr[2] "%(count)s uair an chloig as seo amach"
msgstr[3] "%(count)s uair an chloig as seo amach"
msgstr[4] "%(count)s uair an chloig as seo amach"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s from now"
msgstr "%(delta)s as seo amach"

#. Translators: 'naturaltime-past' strings will be included in '%(delta)s ago'
#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d bhliain"
msgstr[1] "%(num)d bliain"
msgstr[2] "%(num)d bliain"
msgstr[3] "%(num)d bliain"
msgstr[4] "%(num)d bliain"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d mí"
msgstr[1] "%(num)d mí"
msgstr[2] "%(num)d mí"
msgstr[3] "%(num)d mí"
msgstr[4] "%(num)d mí"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d seachtain"
msgstr[1] "%(num)d seachtain"
msgstr[2] "%(num)d seachtain"
msgstr[3] "%(num)d seachtain"
msgstr[4] "%(num)d seachtain"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d lá"
msgstr[1] "%(num)d lá"
msgstr[2] "%(num)d lá"
msgstr[3] "%(num)d lá"
msgstr[4] "%(num)d lá"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d uair"
msgstr[1] "%(num)d uair an chloig"
msgstr[2] "%(num)d uair an chloig"
msgstr[3] "%(num)d uair an chloig"
msgstr[4] "%(num)d uair an chloig"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d nóiméad"
msgstr[1] "%(num)d nóiméad"
msgstr[2] "%(num)d nóiméad"
msgstr[3] "%(num)d nóiméad"
msgstr[4] "%(num)d nóiméad"

#. Translators: 'naturaltime-future' strings will be included in '%(delta)s
#. from now'
#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d bhliain"
msgstr[1] "%(num)d bliain"
msgstr[2] "%(num)d bliain"
msgstr[3] "%(num)d bliain"
msgstr[4] "%(num)d bliain"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d mí"
msgstr[1] "%(num)d mí"
msgstr[2] "%(num)d mí"
msgstr[3] "%(num)d mí"
msgstr[4] "%(num)d mí"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d seachtain"
msgstr[1] "%(num)d seachtain"
msgstr[2] "%(num)d seachtain"
msgstr[3] "%(num)d seachtain"
msgstr[4] "%(num)d seachtain"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d lá"
msgstr[1] "%(num)d lá"
msgstr[2] "%(num)d lá"
msgstr[3] "%(num)d lá"
msgstr[4] "%(num)d lá"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d uair"
msgstr[1] "%(num)d uair an chloig"
msgstr[2] "%(num)d uair an chloig"
msgstr[3] "%(num)d uair an chloig"
msgstr[4] "%(num)d uair an chloig"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d nóiméad"
msgstr[1] "%(num)d nóiméad"
msgstr[2] "%(num)d nóiméad"
msgstr[3] "%(num)d nóiméad"
msgstr[4] "%(num)d nóiméad"
