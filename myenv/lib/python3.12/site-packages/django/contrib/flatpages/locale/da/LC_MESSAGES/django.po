# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2012
# <PERSON> <<EMAIL>>, 2012,2014-2015,2019
# <PERSON>, 2011
# <PERSON><PERSON> <<EMAIL>>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2019-09-17 18:11+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Danish (http://www.transifex.com/django/django/language/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Advanced options"
msgstr "Avancerede muligheder"

msgid "Flat Pages"
msgstr "Flade sider"

msgid "URL"
msgstr "URL"

msgid ""
"Example: “/about/contact/”. Make sure to have leading and trailing slashes."
msgstr ""
"Eksempel: “/om/kontakt/”. Vær opmærksom på, at der skal være skråstreg både "
"først og sidst."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"Denne værdi må kun indeholde bogstaver, tal, punktum, understreger, "
"bindestreger, skråstreger eller tilder."

msgid "Example: “/about/contact”. Make sure to have a leading slash."
msgstr ""
"Eksempel: “/om/kontakt/”. Vær opmærksom på, at der skal være skråstreg først."

msgid "URL is missing a leading slash."
msgstr "URL mangler en skråstreg i starten."

msgid "URL is missing a trailing slash."
msgstr "URL mangler en afsluttende skråstreg."

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr "En flad side med URL'en %(url)s eksisterer allerede for siden %(site)s"

msgid "title"
msgstr "titel"

msgid "content"
msgstr "indhold"

msgid "enable comments"
msgstr "tillad kommentarer"

msgid "template name"
msgstr "skabelonnavn"

msgid ""
"Example: “flatpages/contact_page.html”. If this isn’t provided, the system "
"will use “flatpages/default.html”."
msgstr ""
"Eksempel: “flatpages/kontaktside”. Hvis dette ikke gives bruger systemet "
"“flatpages/default”."

msgid "registration required"
msgstr "registrering påkrævet"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""
"Hvis denne boks er markeret, vil kun brugere der er logget ind, kunne se "
"siden."

msgid "sites"
msgstr "websider"

msgid "flat page"
msgstr "flad side"

msgid "flat pages"
msgstr "flade sider"
