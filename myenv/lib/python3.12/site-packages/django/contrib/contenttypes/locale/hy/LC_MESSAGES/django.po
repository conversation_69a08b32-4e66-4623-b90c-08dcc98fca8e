# This file is distributed under the same license as the Django package.
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-01-17 11:07+0100\n"
"PO-Revision-Date: 2018-11-01 20:28+0000\n"
"Last-Translator: <PERSON><PERSON> <r<PERSON><PERSON><PERSON><PERSON>@mail.ru>\n"
"Language-Team: Armenian (http://www.transifex.com/django/django/language/"
"hy/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: hy\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr "Պարունակության տիպեր"

msgid "python model class name"
msgstr "python մոդելի դասի անուն"

msgid "content type"
msgstr "պարունակության տիպ"

msgid "content types"
msgstr "պարունակության տիպեր"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Պարունակության տիպ %(ct_id)s օբյեկտը չունի իր հետ կապված մոդել"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn't exist"
msgstr "Պարունակության տիպ %(ct_id)s %(obj_id)s օբյեկտը գոյություն չունի"

#, python-format
msgid "%(ct_name)s objects don't have a get_absolute_url() method"
msgstr "%(ct_name)s օբյեկտները չունեն get_absolute_url() մեթոդ"
