# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2011-02-02 11:43+0100\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: admin/__init__.py:121
msgid "and"
msgstr "i"

#: admin/__init__.py:123
#, python-format
msgid ""
"Use the left field to do %(model_name)s lookups in the fields %(field_list)s."
msgstr ""
"Użyj pola po lewej, by w<PERSON><PERSON><PERSON> pola %(field_list)s w modelu %(model_name)s."

#: db/models.py:15
msgid "created"
msgstr "utworzony"

#: db/models.py:16
msgid "modified"
msgstr "zmodyfikowany"

#: db/models.py:26
msgid "title"
msgstr "tytuł"

#: db/models.py:27
msgid "slug"
msgstr "slug"

#: db/models.py:28
msgid "description"
msgstr "opis"

#: db/models.py:50
msgid "Inactive"
msgstr "Nieaktywny"

#: db/models.py:51
msgid "Active"
msgstr "Aktywny"

#: db/models.py:53
msgid "status"
msgstr "stan"

#: db/models.py:56
msgid "keep empty for an immediate activation"
msgstr "pozostaw puste, by aktywować od razu"

#: db/models.py:58
msgid "keep empty for indefinite activation"
msgstr "pozostaw puste, by nie definiować daty deaktywacji"

#: mongodb/fields/__init__.py:22
#, python-format
msgid "String (up to %(max_length)s)"
msgstr "String (do %(max_length)s znaków)"

#: management/commands/show_urls.py:34
#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s nie jest obiektem typu urlpattern"

#: templates/django_extensions/widgets/foreignkey_searchinput.html:4
msgid "Lookup"
msgstr "Szukaj"

#: validators.py:14
msgid "Control Characters like new lines or tabs are not allowed."
msgstr "Znaki nowej linii i tabulatory nie są dozwolone."

#: validators.py:48
msgid "Leading and Trailing whitespaces are not allowed."
msgstr "Białe znaki na początku i końcu wiersza nie są dozwolone."

#: validators.py:74
msgid "Only a hex string is allowed."
msgstr "Tylko wartość hex jest dozwolona."

#: validators.py:75
#, python-format
msgid "Invalid length. Must be %(length)d characters."
msgstr "Niewłaściwa długość. Musi być %(length)d znaków."

#: validators.py:76
#, python-format
msgid "Ensure that there are more than %(min)s characters."
msgstr "Upewnij się, że jest więcej niż %(min)s znaków."

#: validators.py:77
#, python-format
msgid "Ensure that there are no more than %(max)s characters."
msgstr "Upewnij się, że nie ma więcej niż %(max)s znaków."
