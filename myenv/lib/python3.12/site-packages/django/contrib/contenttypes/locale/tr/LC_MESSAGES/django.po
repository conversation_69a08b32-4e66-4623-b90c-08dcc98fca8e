# This file is distributed under the same license as the Django package.
#
# Translators:
# BouRock, 2019
# BouRock, 2014
# <PERSON><PERSON> <<EMAIL>>, 2011
# <PERSON>rat <PERSON>orlu <<EMAIL>>, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2019-09-17 08:00+0000\n"
"Last-Translator: BouRock\n"
"Language-Team: Turkish (http://www.transifex.com/django/django/language/"
"tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

msgid "Content Types"
msgstr "İçerik Türleri"

msgid "python model class name"
msgstr "python model sınıfı adı"

msgid "content type"
msgstr "içerik türü"

msgid "content types"
msgstr "içerik türleri"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "İçerik türü %(ct_id)s nesnesi ilişkilendirilmiş modele sahip değil"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "İçerik türü %(ct_id)s nesne %(obj_id)s mevcut değil"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "%(ct_name)s nesneleri bir get_absolute_url() yöntemine sahip değiller"
