import pytz
from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.db.models import Q
from .models import ProductItem, ProductItemMasters, normalize_japanese, LineMessageSetting, CustomerCode
from .forms import UploadCSVForm
import tempfile
from django.contrib import messages
import os
from rest_framework.views import APIView
from .serializers import SyncProductCodeSerializer, ProductSerializer
from rest_framework import status
from rest_framework.response import Response
from django.utils import timezone
from django.shortcuts import redirect
from django.contrib.auth import login
import base64
import json
import time
from django.http import HttpResponse, HttpResponseRedirect
from Crypto.Cipher import AES
from .sso_user import sso_login_required
from .admin import load_list_image_product
from django.forms.models import model_to_dict
from .sysn_image import call_url_syns_image
from rest_framework.decorators import api_view
from django.conf import settings
from django.utils.timezone import now, localtime
import requests
from django.http import JsonResponse
@sso_login_required
def import_product_items(request):
    if request.method == 'POST':
        form = UploadCSVForm(request.POST, request.FILES)
        if form.is_valid():
            # Save the uploaded file to a temporary location
            csv_file = request.FILES['csv_file']
            with tempfile.NamedTemporaryFile(delete=False, suffix='.csv') as temp_file:
                temp_file_path = temp_file.name
                for chunk in csv_file.chunks():
                    temp_file.write(chunk)

            # Process the CSV file
            try:
                success_count, error_count, error_messages = ProductItem.import_from_csv(temp_file_path)

                # Display results
                messages.success(
                    request,
                    f'Successfully imported {success_count} product items'
                )

                if error_count > 0:
                    messages.warning(
                        request,
                        f'Failed to import {error_count} product items'
                    )

                    for error in error_messages[:10]:  # Show first 10 errors
                        messages.error(request, error)

                    if len(error_messages) > 10:
                        messages.error(
                            request,
                            f'... and {len(error_messages) - 10} more errors'
                        )

            finally:
                # Clean up the temporary file
                os.unlink(temp_file_path)

            return redirect('import_product_items')
    else:
        form = UploadCSVForm()

    return render(request, 'import_product_items.html', {'form': form})
@sso_login_required
def import_product_item_masters(request):
    if request.method == 'POST':
        form = UploadCSVForm(request.POST, request.FILES)
        if form.is_valid():
            # Save the uploaded file to a temporary location
            csv_file = request.FILES['csv_file']
            with tempfile.NamedTemporaryFile(delete=False, suffix='.csv') as temp_file:
                temp_file_path = temp_file.name
                for chunk in csv_file.chunks():
                    temp_file.write(chunk)

            # Process the CSV file
            try:
                success_count, error_count, error_messages = ProductItemMasters.import_from_csv(temp_file_path)

                # Display results
                messages.success(
                    request,
                    f'Successfully imported {success_count} product item masters'
                )

                if error_count > 0:
                    messages.warning(
                        request,
                        f'Failed to import {error_count} product item masters'
                    )

                    for error in error_messages[:10]:  # Show first 10 errors
                        messages.error(request, error)

                    if len(error_messages) > 10:
                        messages.error(
                            request,
                            f'... and {len(error_messages) - 10} more errors'
                        )

            finally:
                # Clean up the temporary file
                os.unlink(temp_file_path)

            return redirect('import_product_item_masters')
    else:
        form = UploadCSVForm()

    return render(request, 'import_product_item_masters.html', {'form': form})

@sso_login_required
def product_list(request):
    # Get query parameters
    search_query = request.GET.get('search', '')
    is_check_sync = request.GET.get('is_check_sync', '')
    sort_by = request.GET.get('sort', 'product_code')
    page_size = int(request.GET.get('page_size', 20))  # Default 20 items per page

    products = ProductItem.objects
    # Apply search filter if provided
    if search_query:
        products = products.filter(
            Q(product_code__icontains=search_query) |
            Q(product_name_search__icontains=search_query) |
            Q(product_kana_search__icontains=search_query)
        )

    # Apply maker filter if provided
    if is_check_sync:
        products = products.filter(is_check_sync=is_check_sync)

    # Get list of all makers for the filter dropdown

    # Apply sorting
    if sort_by.startswith('-'):
        products = products.order_by(sort_by)
    else:
        products = products.order_by(sort_by)
    # Paginate the results
    page = request.GET.get('page', 1)
    paginator = Paginator(products, page_size)

    try:
        products_page = paginator.page(page)
    except PageNotAnInteger:
        products_page = paginator.page(1)
    except EmptyPage:
        products_page = paginator.page(paginator.num_pages)
    # Calculate page range for pagination display
    page_range = get_page_range(paginator, products_page.number)
    sizes = [20, 50, 100]
    tokyo_tz = pytz.timezone("Asia/Tokyo")
    for pro in products_page:
        if pro.check_sync_at is not None:
            pro.check_sync_time_jp = pro.check_sync_at.astimezone(tokyo_tz).strftime("%Y/%m/%d %H:%M")
        pro.created_at_jp = pro.created_at.astimezone(tokyo_tz).strftime("%Y/%m/%d %H:%M")
    context = {
        'loginName': request.session.get('sso_user')['companyCode'],
        'products': products_page,
        'search_query': search_query,
        'is_check_sync': is_check_sync,
        'sort_by': sort_by,
        'page_obj': products_page,
        'page_range': page_range,
        'page_size': page_size,
        'total_count': paginator.count,
        'sizes': sizes,
        'timestamp': int(now().timestamp())
    }

    return render(request, 'product_list.html', context)


def get_page_range(paginator, current_page, window=5):
    """
    Returns a range of page numbers to display in pagination
    with the current page in the center of a window of pages
    """
    total_pages = paginator.num_pages

    # If total pages is less than window, show all pages
    if total_pages <= window:
        return range(1, total_pages + 1)

    # Calculate the window of pages to show
    half_window = window // 2

    # If current page is close to the beginning
    if current_page <= half_window:
        return range(1, window + 1)

    # If current page is close to the end
    if current_page >= total_pages - half_window:
        return range(total_pages - window + 1, total_pages + 1)

    # Current page is in the middle
    return range(current_page - half_window, current_page + half_window + 1)

@sso_login_required
def product_detail(request, product_id):
    """
    View to display product details
    """
    product = get_object_or_404(ProductItem, id=product_id)
    if product.sync_product_code :
        productItemMaster = get_object_or_404(ProductItemMasters, product_code=product.sync_product_code)
    else:
        productItemMaster = None
    # Get query parameters
    search_query = request.GET.get('search', '')
    search_query_search = normalize_japanese(request.GET.get('search', ''))
    maker_name = request.GET.get('maker_name', '')
    sort_by = request.GET.get('sort', 'product_code')
    page_size = int(request.GET.get('page_size', 20))  # Default 20 items per page
    products = ProductItemMasters.objects
    # Apply search filter if provided
    if search_query:
        products = products.filter(
            Q(individual_item_jan_code=search_query_search) |
            Q(product_name_search__icontains=search_query_search) |
            Q(product_kana_search__icontains=search_query_search)
        )

    # Apply maker filter if provided
    if maker_name:
        products = products.filter(manufacturer_name=maker_name)

    # Get list of all makers for the filter dropdown

    # Apply sorting
    if sort_by.startswith('-'):
        products = products.order_by(sort_by)
    else:
        products = products.order_by(sort_by)
    # Paginate the results
    page = request.GET.get('page', 1)
    paginator = Paginator(products, page_size)

    try:
        products_page = paginator.page(page)
    except PageNotAnInteger:
        products_page = paginator.page(1)
    except EmptyPage:
        products_page = paginator.page(paginator.num_pages)
    page_range = get_page_range(paginator, products_page.number)
    maker_list = ProductItemMasters.objects.values_list('manufacturer_name', flat=True).distinct()
    sizes = [20, 50, 100]
    list_images = []
    # load list image by products_page
    if product.sync_product_code:
        countImage = settings.COUNT_IMAGE
        list_images = load_list_image_product(product.sync_product_code,countImage)

    for item in products_page:
        base_dict = model_to_dict(item)
        listImages = load_list_image_product(base_dict['product_code'],2 )
        item.image = listImages[0] if listImages else ""
        item.list_images = listImages
    context = {
        'loginName': request.session.get('sso_user')['companyCode'],
        'products': products_page,
        'search_query': search_query,
        'maker_name': maker_name,
        'sort_by': sort_by,
        'page_obj': products_page,
        'page_range': page_range,
        'page_size': page_size,
        'total_count': paginator.count,
        'product': product,
        'maker_list': maker_list,
        'sizes': sizes,
        'productItemMaster': productItemMaster,
        'list_images' : list_images,
        'timestamp': int(now().timestamp())
    }

    return render(request, 'product_detail.html', context)


class UpdateSyncProductCodeAPIView(APIView):
    def patch(self, request, id):
        product = get_object_or_404(ProductItem, id=id)
        data = request.data.copy()
        data['is_check_sync'] = '1'
        data['check_sync_at'] = timezone.now()
        serializer = SyncProductCodeSerializer(product, data=data, partial=True)
        if serializer.is_valid():
            serializer.save()
            call_url_syns_image(data['sync_product_code'], product.product_code)
            return Response({'message': 'Sync product code updated successfully'})
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

def sso_login(request):
    SECRET_KEY = base64.urlsafe_b64decode('1XlhilHWUKI8-wnFJamS65PuFh21Koi21amf6lIRg4s=')
    token = request.GET.get('sso_token')
    if not token:
        return HttpResponse("❌ token is required", status=400)
    try:
        raw = base64.b64decode(token)
        iv = raw[:16]
        ciphertext = raw[16:]

        cipher = AES.new(SECRET_KEY, AES.MODE_CBC, iv)
        decrypted = cipher.decrypt(ciphertext)

        # remove padding (PKCS7)
        pad_len = decrypted[-1]
        decrypted = decrypted[:-pad_len]

        user_data = json.loads(decrypted)
    except Exception as e:
        return HttpResponse(f"❌ token invalid: {str(e)}", status=400)

        # check expiration
    if user_data.get("exp", 0) < time.time():
        return HttpResponse("❌ Token expired", status=403)

    user_data = {
        "loginId": user_data.get("loginId"),
        "loginName": user_data.get("loginName"),
        "companyCode": user_data.get("companyCode"),
        "nextUrl": user_data.get("nextUrl"),
    }
    print(user_data)
    request.session['sso_user'] = {
        'loginId': user_data['loginId'],
        'loginName': user_data['loginName'],
        'companyCode': user_data['companyCode'],
    }
    request.session.set_expiry(15 * 60)
    return HttpResponseRedirect("/" + user_data['nextUrl'])
@api_view(['POST'])
def sysn_info_product(request):
    products = request.data
    errors = []

    for product_data in products:
        product_code = product_data.get('product_code')
        if not product_code:
            errors.append({'error': 'Missing product_code', 'data': product_data})
            continue
        product_data['product_kana'] = normalize_japanese(product_data['product_name'])
        product_data['category_code2'] = product_data['category_code1']
        product_data['category_code3'] = product_data['category_code1']
        product_data['category_code4'] = product_data['category_code1']
        product_data['category_code5'] = product_data['category_code1']
        product_data['product_name_search'] = normalize_japanese(product_data['product_name'])
        product_data['product_kana_search'] = normalize_japanese(product_data['product_name'])
        product_data['created_at'] = timezone.now().isoformat()
        product_data['updated_at'] = timezone.now().isoformat()
        try:
            product = ProductItem.objects.get(product_code=product_code)
            serializer = ProductSerializer(product, data=product_data)
        except ProductItem.DoesNotExist:
            # Create new if not exists
            serializer = ProductSerializer(data=product_data)

        if serializer.is_valid():
            serializer.save()
        else:
            errors.append({'product_code': product_code, 'errors': serializer.errors})

    if errors:
        return Response({'status': 'partial_success', 'errors': errors}, status=207)
    return Response({'status': 'success'}, status=200)
def line_dashboard(request):
    context = {
        'timestamp': int(now().timestamp())
    }
    return render(request, 'line_dashboard.html',context)
def create_message_line(request):
    products = ProductItem.objects.all()
    context = {
        'timestamp': int(now().timestamp()),
        'products': products
    }
    return render(request, 'create_message_line.html',context)

@api_view(['GET'])
def get_product_item_by_code(request, product_code):
    try:
        product = ProductItem.objects.get(product_code=product_code)
        serializer = ProductSerializer(product)

        # Get product data and add images
        product_data = serializer.data
        product_data['image'] = ''
        if product_data['is_check_sync']:
            product_data['image'] = settings.URL_SMART_ORDER + '/001/img/001/' + product_code+'_1.jpg'
        return Response(product_data, status=status.HTTP_200_OK)
    except ProductItem.DoesNotExist:
        return Response(
            {'error': f'Product with code {product_code} not found'},
            status=status.HTTP_404_NOT_FOUND
        )
def store_message_line(request):
    if request.method == 'POST':
        # Handle POST request for line_dashboard
        try:
            # Get data from POST request
            customer_codes_str = request.POST.get('customer_codes', '')
            delivery_datetime_str = request.POST.get('delivery_datetime')
            format_type = request.POST.get('format_type', '')
            product_code = request.POST.get('product_code', '')
            management_title = request.POST.get('management_title', '')

            # Parse delivery datetime
            from datetime import datetime
            delivery_datetime = None
            if delivery_datetime_str:
                try:
                    # Parse datetime string (format: 2025-08-15T20:00)
                    delivery_datetime = datetime.strptime(delivery_datetime_str, '%Y-%m-%dT%H:%M')
                except ValueError:
                    try:
                        # Try alternative format with seconds
                        delivery_datetime = datetime.strptime(delivery_datetime_str, '%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        messages.error(request, '配信日付の形式が正しくありません')
                        return redirect('create_message_line')

            # Create LineMessageSetting record
            line_message = LineMessageSetting.objects.create(
                delivery_datetime=delivery_datetime,
                format_type=format_type,
                product_code=product_code,
                management_title=management_title
            )

            # Parse customer codes and create CustomerCode records
            if customer_codes_str:
                # Split by comma and clean up whitespace
                customer_codes = [code.strip() for code in customer_codes_str.split(',') if code.strip()]

                # Create CustomerCode records
                for code in customer_codes:
                    CustomerCode.objects.create(
                        line_message=line_message,
                        code=code
                    )

            messages.success(request, f'LINE メッセージ設定が正常に作成されました。ID: {line_message.id}')
            return redirect('create_message_line')

        except Exception as e:
            # Handle errors
            messages.error(request, f'エラーが発生しました: {str(e)}')
            return redirect('create_message_line')

    # Handle GET request (existing functionality)
    context = {
        'timestamp': int(now().timestamp())
    }
    return render(request, 'line_dashboard.html', context)