from . import jaconv
from _typeshed import Incomplete

__all__ = ['hira2kata', 'hira2hkata', 'kata2hira', 'h2z', 'z2h', 'hankaku2zenkaku', 'zenkaku2hankaku', 'normalize', 'kana2alphabet', 'alphabet2kana', 'kata2alphabet', 'alphabet2kata', 'hiragana2julius', 'enlargesmallkana']

hira2kata = jaconv.hira2kata
hira2hkata = jaconv.hira2hkata
kata2hira = jaconv.kata2hira
h2z = jaconv.h2z
z2h = jaconv.z2h
han2zen = jaconv.h2z
zen2han = jaconv.z2h
hankaku2zenkaku = jaconv.h2z
zenkaku2hankaku = jaconv.z2h
normalize = jaconv.normalize
kana2alphabet = jaconv.kana2alphabet
alphabet2kana = jaconv.alphabet2kana
kata2alphabet: Incomplete
alphabet2kata: Incomplete
hiragana2julius = jaconv.hiragana2julius
enlargesmallkana = jaconv.enlargesmallkana
