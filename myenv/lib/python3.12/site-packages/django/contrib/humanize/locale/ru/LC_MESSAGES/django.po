# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<PERSON><PERSON>@mail.ru>, 2014
# <PERSON><PERSON><PERSON> <d<PERSON><PERSON>.<EMAIL>>, 2011
# <PERSON> <<EMAIL>>, 2012
# <PERSON><PERSON><PERSON> (aka greg) <<EMAIL>>, 2018
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <PERSON><PERSON> <<PERSON>@mail.ru>, 2014
# <AUTHOR> <EMAIL>, 2018
# <AUTHOR> <EMAIL>, 2012,2014,2022
# Bob<PERSON><PERSON> <<EMAIL>>, 2018
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-04-07 14:40+0200\n"
"PO-Revision-Date: 2022-04-24 18:40+0000\n"
"Last-Translator: Алексе<PERSON> Борискин <<EMAIL>>, 2012,2014,2022\n"
"Language-Team: Russian (http://www.transifex.com/django/django/language/"
"ru/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ru\n"
"Plural-Forms: nplurals=4; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<12 || n%100>14) ? 1 : n%10==0 || (n%10>=5 && n%10<=9) || "
"(n%100>=11 && n%100<=14)? 2 : 3);\n"

msgid "Humanize"
msgstr "Приведение значений к виду, понятному человеку"

#. Translators: Ordinal format for 11 (11th), 12 (12th), and 13 (13th).
msgctxt "ordinal 11, 12, 13"
msgid "{}th"
msgstr "{}-й"

#. Translators: Ordinal format when value ends with 0, e.g. 80th.
msgctxt "ordinal 0"
msgid "{}th"
msgstr "{}-й"

#. Translators: Ordinal format when value ends with 1, e.g. 81st, except 11.
msgctxt "ordinal 1"
msgid "{}st"
msgstr "{}-й"

#. Translators: Ordinal format when value ends with 2, e.g. 82nd, except 12.
msgctxt "ordinal 2"
msgid "{}nd"
msgstr "{}-й"

#. Translators: Ordinal format when value ends with 3, e.g. 83th, except 13.
msgctxt "ordinal 3"
msgid "{}rd"
msgstr "{}-й"

#. Translators: Ordinal format when value ends with 4, e.g. 84th.
msgctxt "ordinal 4"
msgid "{}th"
msgstr "{}-й"

#. Translators: Ordinal format when value ends with 5, e.g. 85th.
msgctxt "ordinal 5"
msgid "{}th"
msgstr "{}-й"

#. Translators: Ordinal format when value ends with 6, e.g. 86th.
msgctxt "ordinal 6"
msgid "{}th"
msgstr "{}-й"

#. Translators: Ordinal format when value ends with 7, e.g. 87th.
msgctxt "ordinal 7"
msgid "{}th"
msgstr "{}-й"

#. Translators: Ordinal format when value ends with 8, e.g. 88th.
msgctxt "ordinal 8"
msgid "{}th"
msgstr "{}-й"

#. Translators: Ordinal format when value ends with 9, e.g. 89th.
msgctxt "ordinal 9"
msgid "{}th"
msgstr "{}-й"

#, python-format
msgid "%(value)s million"
msgid_plural "%(value)s million"
msgstr[0] "%(value)s миллион"
msgstr[1] "%(value)s миллиона"
msgstr[2] "%(value)s миллионов"
msgstr[3] "%(value)s миллионов"

#, python-format
msgid "%(value)s billion"
msgid_plural "%(value)s billion"
msgstr[0] "%(value)s миллиард"
msgstr[1] "%(value)s миллиарда"
msgstr[2] "%(value)s миллиардов"
msgstr[3] "%(value)s миллиардов"

#, python-format
msgid "%(value)s trillion"
msgid_plural "%(value)s trillion"
msgstr[0] "%(value)s триллион"
msgstr[1] "%(value)s триллиона"
msgstr[2] "%(value)s триллионов"
msgstr[3] "%(value)s триллионов"

#, python-format
msgid "%(value)s quadrillion"
msgid_plural "%(value)s quadrillion"
msgstr[0] "%(value)s квадриллион"
msgstr[1] "%(value)s квадриллиона"
msgstr[2] "%(value)s квадриллионов"
msgstr[3] "%(value)s квадриллионов"

#, python-format
msgid "%(value)s quintillion"
msgid_plural "%(value)s quintillion"
msgstr[0] "%(value)s квинтиллион"
msgstr[1] "%(value)s квинтиллиона"
msgstr[2] "%(value)s квинтиллионов"
msgstr[3] "%(value)s квинтиллионов"

#, python-format
msgid "%(value)s sextillion"
msgid_plural "%(value)s sextillion"
msgstr[0] "%(value)s секстиллион"
msgstr[1] "%(value)s секстиллиона"
msgstr[2] "%(value)s секстиллионов"
msgstr[3] "%(value)s секстиллионов"

#, python-format
msgid "%(value)s septillion"
msgid_plural "%(value)s septillion"
msgstr[0] "%(value)s септиллион"
msgstr[1] "%(value)s септиллиона"
msgstr[2] "%(value)s септиллионов"
msgstr[3] "%(value)s септиллионов"

#, python-format
msgid "%(value)s octillion"
msgid_plural "%(value)s octillion"
msgstr[0] "%(value)s октиллион"
msgstr[1] "%(value)s октиллиона"
msgstr[2] "%(value)s октиллионов"
msgstr[3] "%(value)s октиллионов"

#, python-format
msgid "%(value)s nonillion"
msgid_plural "%(value)s nonillion"
msgstr[0] "%(value)s нониллион"
msgstr[1] "%(value)s нониллиона"
msgstr[2] "%(value)s нониллионов"
msgstr[3] "%(value)s нониллионов"

#, python-format
msgid "%(value)s decillion"
msgid_plural "%(value)s decillion"
msgstr[0] "%(value)s дециллион"
msgstr[1] "%(value)s дециллиона"
msgstr[2] "%(value)s дециллионов"
msgstr[3] "%(value)s дециллионов"

#, python-format
msgid "%(value)s googol"
msgid_plural "%(value)s googol"
msgstr[0] "%(value)s гугол"
msgstr[1] "%(value)s гугола"
msgstr[2] "%(value)s гуголов"
msgstr[3] "%(value)s гуголов"

msgid "one"
msgstr "один"

msgid "two"
msgstr "два"

msgid "three"
msgstr "три"

msgid "four"
msgstr "четыре"

msgid "five"
msgstr "пять"

msgid "six"
msgstr "шесть"

msgid "seven"
msgstr "семь"

msgid "eight"
msgstr "восемь"

msgid "nine"
msgstr "девять"

msgid "today"
msgstr "сегодня"

msgid "tomorrow"
msgstr "завтра"

msgid "yesterday"
msgstr "вчера"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s ago"
msgstr "%(delta)s назад"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour ago"
msgid_plural "%(count)s hours ago"
msgstr[0] "%(count)s час назад"
msgstr[1] "%(count)s часа назад"
msgstr[2] "%(count)s часов назад"
msgstr[3] "%(count)s часов назад"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute ago"
msgid_plural "%(count)s minutes ago"
msgstr[0] "%(count)s минуту назад"
msgstr[1] "%(count)s минуты назад"
msgstr[2] "%(count)s минут назад"
msgstr[3] "%(count)s минут назад"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second ago"
msgid_plural "%(count)s seconds ago"
msgstr[0] "%(count)s секунду назад"
msgstr[1] "%(count)s секунды назад"
msgstr[2] "%(count)s секунд назад"
msgstr[3] "%(count)s секунд назад"

msgid "now"
msgstr "сейчас"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second from now"
msgid_plural "%(count)s seconds from now"
msgstr[0] "через %(count)s секунду"
msgstr[1] "через %(count)s секунды"
msgstr[2] "через %(count)s секунд"
msgstr[3] "через %(count)s секунд"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute from now"
msgid_plural "%(count)s minutes from now"
msgstr[0] "через %(count)s минуту"
msgstr[1] "через %(count)s минуты"
msgstr[2] "через %(count)s минут"
msgstr[3] "через %(count)s минут"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour from now"
msgid_plural "%(count)s hours from now"
msgstr[0] "через %(count)s час"
msgstr[1] "через %(count)s часа"
msgstr[2] "через %(count)s часов"
msgstr[3] "через %(count)s часов"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s from now"
msgstr "через %(delta)s"

#. Translators: 'naturaltime-past' strings will be included in '%(delta)s ago'
#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d год"
msgstr[1] "%(num)d года"
msgstr[2] "%(num)d лет"
msgstr[3] "%(num)d лет"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d месяц"
msgstr[1] "%(num)d месяца"
msgstr[2] "%(num)d месяцев"
msgstr[3] "%(num)d месяцев"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d неделю"
msgstr[1] "%(num)d недели"
msgstr[2] "%(num)d недель"
msgstr[3] "%(num)d недель"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d день"
msgstr[1] "%(num)d дня"
msgstr[2] "%(num)d дней"
msgstr[3] "%(num)d дней"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d час"
msgstr[1] "%(num)d часа"
msgstr[2] "%(num)d часов"
msgstr[3] "%(num)d часов"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d минуту"
msgstr[1] "%(num)d минуты"
msgstr[2] "%(num)d минут"
msgstr[3] "%(num)d минут"

#. Translators: 'naturaltime-future' strings will be included in '%(delta)s
#. from now'
#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d год"
msgstr[1] "%(num)d года"
msgstr[2] "%(num)d лет"
msgstr[3] "%(num)d лет"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d месяц"
msgstr[1] "%(num)d месяца"
msgstr[2] "%(num)d месяцев"
msgstr[3] "%(num)d месяцев"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d неделю"
msgstr[1] "%(num)d недели"
msgstr[2] "%(num)d недель"
msgstr[3] "%(num)d недель"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d день"
msgstr[1] "%(num)d дня"
msgstr[2] "%(num)d дней"
msgstr[3] "%(num)d дней"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d час"
msgstr[1] "%(num)d часа"
msgstr[2] "%(num)d часов"
msgstr[3] "%(num)d часов"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d минуту"
msgstr[1] "%(num)d минуты"
msgstr[2] "%(num)d минут"
msgstr[3] "%(num)d минут"
