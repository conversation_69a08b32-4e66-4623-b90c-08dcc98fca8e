# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2011
# Jon, 2015-2016
# <PERSON>, 2014
# Jon, 2020-2021
# <PERSON>, 2013
# Jon, 2012
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-03-18 14:16+0000\n"
"Last-Translator: Jon\n"
"Language-Team: Norwegian Bokmål (http://www.transifex.com/django/django/"
"language/nb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: nb\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Administrative Documentation"
msgstr "Administrasjons-dokumentasjon"

msgid "Home"
msgstr "Hjem"

msgid "Documentation"
msgstr "Dokumentasjon"

msgid "Bookmarklets"
msgstr "Bokmerker"

msgid "Documentation bookmarklets"
msgstr "Dokumentasjonsbokmerker"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"For å installere bokmerker, dra linken til verktøylinjen for bokmerker, "
"eller høyreklikk og legg til i bokmerker. Nå kan du du velge bokmerket fra "
"hvilken som helst side på nettstedet."

msgid "Documentation for this page"
msgstr "Dokumentasjon for denne siden"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Hopp fra hvilken som helst side til dokumentasjonen for visningsfunksjonen "
"som genererte den siden."

msgid "Tags"
msgstr "Tags"

msgid "List of all the template tags and their functions."
msgstr "Liste over alle mal-taggene og deres funksjoner."

msgid "Filters"
msgstr "Filter"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Filtre er handlinger som kan brukes på variabler i en mal for å endre output."

msgid "Models"
msgstr "Modeller"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Modeller er beskrivelser av alle objektene i systemet og tilhørende felter. "
"Hver modell har en liste over felt som kan brukes som mal-variabler"

msgid "Views"
msgstr "Views"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Hver side på det offentlige nettstedet er generert av et view. View'et "
"definerer hvilken malen som blir brukt til å generere siden og hvilke "
"objekter som er tilgjengelige for den malen."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Verktøy for nettleseren din for å få rask tilgang til admin-funksjonalitet."

msgid "Please install docutils"
msgstr "Vennligst installer docutils"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"Admin-verktøyets dokumentasjonssystem behøver Pythons <a href=\"%(link)s"
"\">docutils</a>-bibliotek ."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Spør dine administratorer om å installere <a href=\"%(link)s\">docutils</a> ."

#, python-format
msgid "Model: %(name)s"
msgstr "Modell: %(name)s"

msgid "Fields"
msgstr "Felter"

msgid "Field"
msgstr "Felt"

msgid "Type"
msgstr "Type"

msgid "Description"
msgstr "Beskrivelse"

msgid "Methods with arguments"
msgstr "Metoder med argumenter"

msgid "Method"
msgstr "Metode"

msgid "Arguments"
msgstr "Argumenter"

msgid "Back to Model documentation"
msgstr "Tilbake til modell-dokumentasjon"

msgid "Model documentation"
msgstr "Modelldokumentasjon"

msgid "Model groups"
msgstr "Modellgrupper"

msgid "Templates"
msgstr "Maler"

#, python-format
msgid "Template: %(name)s"
msgstr "Mal: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Mal: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Søkebane for mal <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(finnes ikke)"

msgid "Back to Documentation"
msgstr "Tilbake til dokumentasjonen"

msgid "Template filters"
msgstr "Mal-filtre"

msgid "Template filter documentation"
msgstr "Mal-filter dokumentasjon"

msgid "Built-in filters"
msgstr "Innebygde filtre"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"For å bruke disse filtrene, sett inn <code>%(code)s</code> i malen før du "
"bruker filteret."

msgid "Template tags"
msgstr "Mal-tagger"

msgid "Template tag documentation"
msgstr "Mal-tag-dokumentasjon"

msgid "Built-in tags"
msgstr "Innebygde tagger"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"For å bruke disse taggene, sett inn <code>%(code)s</code> i malen før du "
"bruker taggen."

#, python-format
msgid "View: %(name)s"
msgstr "View: %(name)s"

msgid "Context:"
msgstr "Kontekst:"

msgid "Templates:"
msgstr "Maler:"

msgid "Back to View documentation"
msgstr "Tilbake til view-dokumentasjonen"

msgid "View documentation"
msgstr "View-dokumentasjon"

msgid "Jump to namespace"
msgstr "Gå til navnerom"

msgid "Empty namespace"
msgstr "Tomt navnerom"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Views etter navnerom %(name)s"

msgid "Views by empty namespace"
msgstr "Views etter tomt navnerom"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"View-funksjon: <code>%(full_name)s</code>. Navn: <code>%(url_name)s</code>.\n"

msgid "tag:"
msgstr "tag:"

msgid "filter:"
msgstr "filter:"

msgid "view:"
msgstr "view:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Applikasjon %(app_label)r ikke funnet"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Fant ikke modellen %(model_name)r i applikasjonen %(app_label)r"

msgid "model:"
msgstr "modell:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "det relaterte `%(app_label)s.%(data_type)s`-objektet"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "relaterte `%(app_label)s.%(object_name)s`-objekter"

#, python-format
msgid "all %s"
msgstr "alle %s"

#, python-format
msgid "number of %s"
msgstr "antall %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s ser ikke ut til å være et urlpattern-objekt"
