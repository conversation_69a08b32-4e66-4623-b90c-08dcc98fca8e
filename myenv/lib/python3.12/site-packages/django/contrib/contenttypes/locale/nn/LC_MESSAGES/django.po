# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# <AUTHOR> <EMAIL>, 2013
# Sivert <PERSON>, 2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2021-10-21 18:49+0000\n"
"Last-Translator: Sivert <PERSON>\n"
"Language-Team: Norwegian Nynorsk (http://www.transifex.com/django/django/"
"language/nn/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: nn\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr "Innhaldstypar"

msgid "python model class name"
msgstr "python-modell klassenamn"

msgid "content type"
msgstr "innhaldstype"

msgid "content types"
msgstr "innhaldstypar"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "Innhaldstype %(ct_id)s-objektet har ingen modell knytta til seg"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "Innhaldstype %(ct_id)s-objekt %(obj_id)s eksisterer ikkje"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "%(ct_name)s-objekt har ikkje ein get_absolute_url() metode"
