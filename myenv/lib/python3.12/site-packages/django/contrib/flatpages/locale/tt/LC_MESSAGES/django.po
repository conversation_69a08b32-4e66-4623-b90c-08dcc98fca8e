# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2011
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2015-10-09 17:42+0200\n"
"PO-Revision-Date: 2017-09-19 16:40+0000\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Tatar (http://www.transifex.com/django/django/language/tt/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: tt\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgid "Advanced options"
msgstr "Өстәмә көйләүләр"

msgid "Flat Pages"
msgstr ""

msgid "URL"
msgstr "URL"

msgid ""
"Example: '/about/contact/'. Make sure to have leading and trailing slashes."
msgstr ""
"Үрнәк: '/about/contact/'. Алдынгы һәм арттагы авыш сызыкларын кертүне "
"тикшерегез."

msgid ""
"This value must contain only letters, numbers, dots, underscores, dashes, "
"slashes or tildes."
msgstr ""
"Бу кыйммәт тик хәрефләрдән, саннардан һәм нокта, астына сызу, тире, авыш "
"сызык, тильда билгеләреннән генә тора ала."

msgid "URL is missing a leading slash."
msgstr ""

msgid "URL is missing a trailing slash."
msgstr ""

#, python-format
msgid "Flatpage with url %(url)s already exists for site %(site)s"
msgstr ""

msgid "title"
msgstr "башлам"

msgid "content"
msgstr "эчтәлек"

msgid "enable comments"
msgstr "фикерләрне ялгарга"

msgid "template name"
msgstr "шаблон исеме"

msgid ""
"Example: 'flatpages/contact_page.html'. If this isn't provided, the system "
"will use 'flatpages/default.html'."
msgstr ""
"Үрнәк: 'flatpages/contact_page.html'. Күрсәтелгән булмаса, система "
"'flatpages/defalut.html' кулланыр."

msgid "registration required"
msgstr "теркәлү кирәк"

msgid "If this is checked, only logged-in users will be able to view the page."
msgstr ""
"Билгеләнгән булса, фәкать кергән кулланучылар гына сәхифәне күрә алырлар."

msgid "sites"
msgstr ""

msgid "flat page"
msgstr "гади сәхифә"

msgid "flat pages"
msgstr "гади сәхифәләр"
