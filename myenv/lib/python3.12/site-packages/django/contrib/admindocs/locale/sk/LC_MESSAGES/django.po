# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON><PERSON> <jann<PERSON>@leidel.info>, 2011
# 18f25ad6fa9930fc67cb11aca9d16a27, 2012-2013
# <PERSON> <<EMAIL>>, 2012-2013,2015,2017
# <PERSON> <<EMAIL>>, 2017,2023
# <PERSON>, 2021
# su<PERSON>wski <<EMAIL>>, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2023-12-04 20:19+0000\n"
"Last-Translator: <PERSON> <ezi<PERSON>@gmail.com>, 2017,2023\n"
"Language-Team: Slovak (http://app.transifex.com/django/django/language/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n "
">= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

msgid "Administrative Documentation"
msgstr "Správcovská dokumentácia"

msgid "Home"
msgstr "Domov"

msgid "Documentation"
msgstr "Dokumentácia"

msgid "Bookmarklets"
msgstr "Záložky"

msgid "Documentation bookmarklets"
msgstr "Záložky dokumentácie"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Na nainštalovanie bookmarkletov, pretiahnite odkaz váš panel so záložkami, "
"alebo kliknite pravým tlačidlom na odkaz a pridajte ho do vašich záložiek. "
"Teraz si môžete vybrať bookmarklet z ľubovoľnej stránky na sídle."

msgid "Documentation for this page"
msgstr "Dokumentácia pre túto stránku"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Nasmeruje vás z ľubovoľnej stránky do dokumentácie, kde je popísané, ako sa "
"táto stránka generuje."

msgid "Tags"
msgstr "Značky"

msgid "List of all the template tags and their functions."
msgstr "Zoznam všetkých šablónových značiek a ich funkcií."

msgid "Filters"
msgstr "Filtre"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Filtre su akcie, ktoré môžu byť aplikované na premenné v šablóne, aby "
"zmenili ich výstup."

msgid "Models"
msgstr "Modely"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Modely sú popisom všetkých objektov v systéme a ich pridružených polí. Každý "
"model má zoznam polí, ktoré sú dostupné ako premenné v šablónach."

msgid "Views"
msgstr "Zobrazenia"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Každá stránka na verejnom sídle je generovaná pomocou pohľadu. Pohľad "
"definuje, ktorá šablóna sa použije na generovanie stránky, a ktoré objekty "
"budú v šablóne k dispozícii."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Nástroje pre váš prohliadač k rýchlemu prístupu k funkciám správy stránok."

msgid "Please install docutils"
msgstr "Prosím, nainštalujte docutils"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a "
"href=\"%(link)s\">docutils</a> library."
msgstr ""
"Dokumentačný systém správy stránok vyžaduje Python knižnicu <a "
"href=\"%(link)s\">docutils</a>."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Požiadajte administrátorov o inštaláciu <a href=\"%(link)s\">docutils</a>."

#, python-format
msgid "Model: %(name)s"
msgstr "Model: %(name)s"

msgid "Fields"
msgstr "Polia"

msgid "Field"
msgstr "Pole"

msgid "Type"
msgstr "Typ"

msgid "Description"
msgstr "Popis"

msgid "Methods with arguments"
msgstr "Metódy s argumentami"

msgid "Method"
msgstr "Metódy"

msgid "Arguments"
msgstr "Argumenty"

msgid "Back to Model documentation"
msgstr "Späť na dokumentáciu modelov"

msgid "Model documentation"
msgstr "Dokumentácia modelu"

msgid "Model groups"
msgstr "Skupiny modelov"

msgid "Templates"
msgstr "Šablóny"

#, python-format
msgid "Template: %(name)s"
msgstr "Šablóna: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Šablóna: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Vyhľadávacia cesta pre šablónu <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(neexistuje)"

msgid "Back to Documentation"
msgstr "Späť na Dokumentáciu"

msgid "Template filters"
msgstr "Filtre šablóny"

msgid "Template filter documentation"
msgstr "Dokumentácia filtrov šablón"

msgid "Built-in filters"
msgstr "Vstavané filtre"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Na použitie týchto filtrov, vložte <code>%(code)s</code> do vašej šablóny "
"pred použitím filtra."

msgid "Template tags"
msgstr "Šablónové značky"

msgid "Template tag documentation"
msgstr "Dokumentácia šablónových značiek"

msgid "Built-in tags"
msgstr "Vstavané značky"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Na použitie týchto značiek, vložte <code>%(code)s</code> do vašej šablóny "
"pred použitím značky."

#, python-format
msgid "View: %(name)s"
msgstr "Pohľad: %(name)s"

msgid "Context:"
msgstr "Kontext:"

msgid "Templates:"
msgstr "Šablóny:"

msgid "Back to View documentation"
msgstr "Späť na dokumentáciu pohľadov"

msgid "View documentation"
msgstr "Dokumentácia pohľadov"

msgid "Jump to namespace"
msgstr "Prejsť na menný priestor"

msgid "Empty namespace"
msgstr "Prázdny menný priestor"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Pohľady podľa menného priestoru %(name)s"

msgid "Views by empty namespace"
msgstr "Pohľady podľa prázdneho menného priestoru"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"Funkcia pohľadu: <code>%(full_name)s</code>. Názov: <code>%(url_name)s</"
"code>.\n"

msgid "tag:"
msgstr "značka:"

msgid "filter:"
msgstr "filter:"

msgid "view:"
msgstr "pohľad:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Applikácia %(app_label)r nenájdená"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Model %(model_name)r sa v aplikácii %(app_label)r nenachádza"

msgid "model:"
msgstr "model:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "súvisiaci objekt `%(app_label)s.%(data_type)s`"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "súvisiace objekty `%(app_label)s.%(object_name)s`"

#, python-format
msgid "all %s"
msgstr "všetky %s"

#, python-format
msgid "number of %s"
msgstr "počet %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "%s nevyzerá ako urlpattern objekt"
