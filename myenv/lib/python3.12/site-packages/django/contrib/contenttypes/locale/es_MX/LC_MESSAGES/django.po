# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON>, 2011-2012
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2019-12-25 05:47+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Spanish (Mexico) (http://www.transifex.com/django/django/"
"language/es_MX/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: es_MX\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

msgid "Content Types"
msgstr "Tipos de Contenido"

msgid "python model class name"
msgstr "nombre de la clase python del modelo"

msgid "content type"
msgstr "tipo de contenido"

msgid "content types"
msgstr "tipos de contenido"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr ""
"Los objetos con el tipo de contenido %(ct_id)s no tienen un modelo asociado"

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr ""

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr ""
