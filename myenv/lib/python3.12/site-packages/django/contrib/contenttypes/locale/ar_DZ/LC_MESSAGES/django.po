# This file is distributed under the same license as the Django package.
#
# Translators:
# <AUTHOR> <EMAIL>, 2019
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2019-09-08 17:27+0200\n"
"PO-Revision-Date: 2019-12-14 18:35+0000\n"
"Last-Translator: Riterix <<EMAIL>>\n"
"Language-Team: Arabic (Algeria) (http://www.transifex.com/django/django/"
"language/ar_DZ/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: ar_DZ\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 "
"&& n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"

msgid "Content Types"
msgstr "نوع المحتوى"

msgid "python model class name"
msgstr "اسم صنف النموذج في python"

msgid "content type"
msgstr "نوع البيانات"

msgid "content types"
msgstr "أنواع البيانات"

#, python-format
msgid "Content type %(ct_id)s object has no associated model"
msgstr "لا يوجد كائن مرتبط بنوع البيانات %(ct_id)s ."

#, python-format
msgid "Content type %(ct_id)s object %(obj_id)s doesn’t exist"
msgstr "نوع المحتوى %(ct_id)s الكائن %(obj_id)s غير موجود"

#, python-format
msgid "%(ct_name)s objects don’t have a get_absolute_url() method"
msgstr "%(ct_name)s كائن لا يحتوي على دالة get_absolute_url() ."
