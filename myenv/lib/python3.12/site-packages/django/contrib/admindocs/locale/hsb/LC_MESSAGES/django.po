# This file is distributed under the same license as the Django package.
#
# Translators:
# <PERSON> <<EMAIL>>, 2016,2019,2021
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-01-15 09:00+0100\n"
"PO-Revision-Date: 2021-02-01 21:47+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Upper Sorbian (http://www.transifex.com/django/django/"
"language/hsb/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: hsb\n"
"Plural-Forms: nplurals=4; plural=(n%100==1 ? 0 : n%100==2 ? 1 : n%100==3 || n"
"%100==4 ? 2 : 3);\n"

msgid "Administrative Documentation"
msgstr "Administratiwna dokumentacija"

msgid "Home"
msgstr "Startowa strona"

msgid "Documentation"
msgstr "Dokumentacija"

msgid "Bookmarklets"
msgstr "Skriptowe zapołožki"

msgid "Documentation bookmarklets"
msgstr "Skriptowe zapołožki dokumentacije"

msgid ""
"To install bookmarklets, drag the link to your bookmarks toolbar, or right-"
"click the link and add it to your bookmarks. Now you can select the "
"bookmarklet from any page in the site."
msgstr ""
"Zo byšće skriptowe zapołožki instalował, ćehńće wotkaz do swojeje lajsty "
"zapołožkow abo klikńće z prawej tastu myški na wotkaz a přidajće jón k "
"swojim zapołožkam. Móžeće skriptowu zapołožku nětko z kóždeje strony na "
"sydle wubrać."

msgid "Documentation for this page"
msgstr "Dokumentacija za tutu stronu"

msgid ""
"Jumps you from any page to the documentation for the view that generates "
"that page."
msgstr ""
"Zmóžnja wam, wot někajkeje strony do dokumentacije napohlada skočić, kotryž "
"je tu stronu wutworił."

msgid "Tags"
msgstr "Znački"

msgid "List of all the template tags and their functions."
msgstr "Lisćina wšěch předłohowych značkow a jich funkcijow."

msgid "Filters"
msgstr "Filtry"

msgid ""
"Filters are actions which can be applied to variables in a template to alter "
"the output."
msgstr ""
"Filtry su akcije, kotrež dadźa so na wariable w předłoze nałožić, zo bychu "
"wudaće změnili."

msgid "Models"
msgstr "Modele"

msgid ""
"Models are descriptions of all the objects in the system and their "
"associated fields. Each model has a list of fields which can be accessed as "
"template variables"
msgstr ""
"Modele su wopisanja wšěch objektow w systemje a jich přisłušnych polow. "
"Kóždy model ma lisćinu polow, na kotrež maja přistup jako předłohowe "
"wariable."

msgid "Views"
msgstr "Napohlady"

msgid ""
"Each page on the public site is generated by a view. The view defines which "
"template is used to generate the page and which objects are available to "
"that template."
msgstr ""
"Kóžda strona na zjawnym sydle so přez napohlad twori. Napohlad definuje, "
"kotra předłoha so wužiwa, zo by stronu wutworiła a kotre objekty su tej "
"přełoze k dispoziciji."

msgid "Tools for your browser to quickly access admin functionality."
msgstr ""
"Nastroje za waš wobhladowak za spěšny přistup na administratorowu "
"funkcionalnosć."

msgid "Please install docutils"
msgstr "Prošu instalujće docutils"

#, python-format
msgid ""
"The admin documentation system requires Python’s <a href=\"%(link)s"
"\">docutils</a> library."
msgstr ""
"Administratorowy dokumentaciski system sej Pythonowu biblioteku <a href="
"\"%(link)s\">docutils</a> wužaduje."

#, python-format
msgid ""
"Please ask your administrators to install <a href=\"%(link)s\">docutils</a>."
msgstr ""
"Prošu prošće swojich administratorow, <a href=\"%(link)s\">docutils</a> "
"instalować."

#, python-format
msgid "Model: %(name)s"
msgstr "Model: %(name)s"

msgid "Fields"
msgstr "Pola"

msgid "Field"
msgstr "Polo"

msgid "Type"
msgstr "Typ"

msgid "Description"
msgstr "Wopisanje"

msgid "Methods with arguments"
msgstr "Metody z argumentami"

msgid "Method"
msgstr "Metoda"

msgid "Arguments"
msgstr "Argumenty"

msgid "Back to Model documentation"
msgstr "Wróćo k modelowej dokumentaciji"

msgid "Model documentation"
msgstr "Modelowa dokumentacija"

msgid "Model groups"
msgstr "Modelowe skupiny"

msgid "Templates"
msgstr "Předłohi"

#, python-format
msgid "Template: %(name)s"
msgstr "Předłoha: %(name)s"

#, python-format
msgid "Template: <q>%(name)s</q>"
msgstr "Předłoha: <q>%(name)s</q>"

#. Translators: Search is not a verb here, it qualifies path (a search path)
#, python-format
msgid "Search path for template <q>%(name)s</q>:"
msgstr "Pytanska šćežka za předłohu <q>%(name)s</q>:"

msgid "(does not exist)"
msgstr "(njeeksistuje)"

msgid "Back to Documentation"
msgstr "Wróćo k dokumentaciji"

msgid "Template filters"
msgstr "Předłohowe filtry"

msgid "Template filter documentation"
msgstr "Dokumentacija předłohowych filtrow"

msgid "Built-in filters"
msgstr "Zatwarjene filtry"

#, python-format
msgid ""
"To use these filters, put <code>%(code)s</code> in your template before "
"using the filter."
msgstr ""
"Zo byšće tute filtry wužiwał, zasadźće <code>%(code)s</code> do swojeje "
"předłohi, prjedy hač filter wužiwaće."

msgid "Template tags"
msgstr "Předłohowe znački"

msgid "Template tag documentation"
msgstr "Dokumentacija předłohowych značkow"

msgid "Built-in tags"
msgstr "Zatwarjene znački"

#, python-format
msgid ""
"To use these tags, put <code>%(code)s</code> in your template before using "
"the tag."
msgstr ""
"Zo byšće tute znački wužiwał, zasadźće <code>%(code)s</code> do swojeje "
"předłohi, prjedy hač značku wužiwaće."

#, python-format
msgid "View: %(name)s"
msgstr "Napohlad: %(name)s"

msgid "Context:"
msgstr "Kontekst:"

msgid "Templates:"
msgstr "Předłohi:"

msgid "Back to View documentation"
msgstr "Wróćo k napohladowej dokumentaciji"

msgid "View documentation"
msgstr "Napohladowa dokumentacija"

msgid "Jump to namespace"
msgstr "K mjenowemu rumej skočić"

msgid "Empty namespace"
msgstr "Prózdny mjenowy rum"

#, python-format
msgid "Views by namespace %(name)s"
msgstr "Napohlady po mjenowym rumje %(name)s"

msgid "Views by empty namespace"
msgstr "Napohlady po prózdnym mjenowym rumje"

#, python-format
msgid ""
"\n"
"    View function: <code>%(full_name)s</code>. Name: <code>%(url_name)s</"
"code>.\n"
msgstr ""
"\n"
"    Napohladowa funkcija: <code>%(full_name)s</code>. Mjeno: <code>"
"%(url_name)s</code>.\n"

msgid "tag:"
msgstr "značka:"

msgid "filter:"
msgstr "filter:"

msgid "view:"
msgstr "napohlad:"

#, python-format
msgid "App %(app_label)r not found"
msgstr "Nałoženje %(app_label)r njenamakane"

#, python-format
msgid "Model %(model_name)r not found in app %(app_label)r"
msgstr "Model %(model_name)r njeje so w nałoženju %(app_label)r namakał"

msgid "model:"
msgstr "model:"

#, python-format
msgid "the related `%(app_label)s.%(data_type)s` object"
msgstr "přisłušny objekt  %(app_label)s.%(data_type)s“"

#, python-format
msgid "related `%(app_label)s.%(object_name)s` objects"
msgstr "přisłušne objekty %(app_label)s.%(object_name)s“"

#, python-format
msgid "all %s"
msgstr "wšě %s"

#, python-format
msgid "number of %s"
msgstr "ličba %s"

#, python-format
msgid "%s does not appear to be a urlpattern object"
msgstr "zda so, zo %s objekt urlpattern njeje"
